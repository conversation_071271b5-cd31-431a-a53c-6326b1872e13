# Step 2 Improvements: Automated DNS Selection

## Overview
Step 2 has been updated to automatically select DNS servers based on domain and location instead of requiring them as command line arguments.

## Changes Made

### **1. Added DNS Mappings**

The script now includes a comprehensive DNS mapping table:

```python
DNS_MAPPINGS = {
    ("ESI", "COLO"): ("************", "*************"),
    ("ESI", "<PERSON>utley"): ("************", "************"),
    ("ESI", "Baltimore"): ("************", "************"),
    ("ESI", "Raleigh"): ("*************", "*************"),
    ("EGC", "COLO"): ("************", "*************"),
    ("EGC", "Nutley"): ("************", "************"),
    ("ESI", "AWS"): ("************", "************"),
    ("EGC", "AWS"): ("***********", "************")
}
```

### **2. Added Server Uptime Check**

```python
def check_server_uptime(session):
    """
    Checks the uptime of the server using PowerShell.
    Returns True if the server is up, otherwise False.
    """
```

This function verifies the server is responsive before proceeding with IP configuration.

### **3. Updated Function Signature**

#### **Before:**
```python
def update_ip_config(host, username, password, ip_address, subnet_mask, gateway, dns_servers):
```

#### **After:**
```python
def update_ip_config(host, username, password, ip_address, subnet_mask, gateway, domain, location):
```

### **4. Automated DNS Server Selection**

The script now automatically selects the correct DNS servers based on domain and location:

```python
# Get DNS settings based on domain and location
dns_settings = DNS_MAPPINGS.get((domain, location))
if not dns_settings:
    logging.error(f"Invalid domain ({domain}) or location ({location}) provided.")
    return False

primary_dns, secondary_dns = dns_settings
```

### **5. Enhanced PowerShell Scripts**

#### **IP Configuration Script:**
```powershell
$adapter = Get-NetAdapter | Where-Object { $_.Status -eq "Up" } | Select-Object -First 1
$interfaceIndex = $adapter.InterfaceIndex

# Remove existing IP configuration
Remove-NetIPAddress -InterfaceIndex $interfaceIndex -Confirm:$false -ErrorAction SilentlyContinue
Remove-NetRoute -InterfaceIndex $interfaceIndex -Confirm:$false -ErrorAction SilentlyContinue

# Set new IP configuration
New-NetIPAddress -InterfaceIndex $interfaceIndex -IPAddress '{ip_address}' -PrefixLength {subnet_mask} -DefaultGateway '{gateway}'
```

#### **DNS Configuration Script:**
```powershell
$adapter = Get-NetAdapter | Where-Object { $_.Status -eq "Up" } | Select-Object -First 1
Set-DnsClientServerAddress -InterfaceAlias $adapter.Name -ServerAddresses "{primary_dns}", "{secondary_dns}"
```

### **6. Added 5-Minute Delay**

The script now includes a 5-minute delay before attempting connection:

```python
# Wait for 5 minutes before attempting connection
logging.info("Waiting for 5 minutes before attempting connection to the server...")
time.sleep(300)
```

### **7. Updated Usage**

#### **New Command Format:**
```bash
python step2_update_ip_config.py HOST USERNAME PASSWORD IP_ADDRESS SUBNET_MASK GATEWAY DOMAIN LOCATION

# Example:
python step2_update_ip_config.py ************* domain\admin Password123 **********0 24 ********** ESI COLO
```

#### **Arguments:**
1. **HOST** - Target server IP/hostname
2. **USERNAME** - WinRM username
3. **PASSWORD** - WinRM password
4. **IP_ADDRESS** - New IP address
5. **SUBNET_MASK** - Subnet mask in CIDR notation (e.g., 24)
6. **GATEWAY** - Default gateway IP
7. **DOMAIN** - Domain (ESI or EGC)
8. **LOCATION** - Location (COLO, Nutley, Baltimore, Raleigh, AWS)

### **8. Enhanced Error Handling**

- **DNS mapping validation** - Checks if domain/location combination is valid
- **Server uptime verification** - Ensures server is responsive
- **PowerShell command status checking** - Validates both IP and DNS configuration
- **Comprehensive exception handling** - Catches and logs all errors

### **9. Improved Logging**

All operations are now properly logged with detailed information:

```python
logging.info(f"IP Address set to: {ip_address}")
logging.info(f"Subnet Mask set to: /{subnet_mask}")
logging.info(f"Default Gateway set to: {gateway}")
logging.info(f"DNS Servers set to: {primary_dns}, {secondary_dns}")
logging.info(f"DNS updated successfully for Domain: {domain}, Location: {location}")
```

## Benefits

### **1. Simplified Usage**
- ✅ No need to manually specify DNS servers
- ✅ Automatic DNS selection based on domain/location
- ✅ Reduced chance of DNS configuration errors

### **2. Location Awareness**
- ✅ Supports multiple locations per domain
- ✅ Automatic DNS server mapping
- ✅ Consistent DNS configuration across environments

### **3. Enhanced Reliability**
- ✅ Server uptime verification before configuration
- ✅ 5-minute delay for server stabilization
- ✅ Comprehensive error handling and logging

### **4. Improved PowerShell Scripts**
- ✅ More robust IP configuration handling
- ✅ Automatic cleanup of existing configuration
- ✅ Better error handling in PowerShell commands

## Usage Examples

### **ESI Domain - COLO Location:**
```bash
python step2_update_ip_config.py ************* domain\admin Password123 **********0 24 ********** ESI COLO
```
**DNS Servers:** ************, *************

### **ESI Domain - Nutley Location:**
```bash
python step2_update_ip_config.py ************* domain\admin Password123 **********0 24 ********** ESI Nutley
```
**DNS Servers:** ************, ************

### **EGC Domain - AWS Location:**
```bash
python step2_update_ip_config.py ************* domain\admin Password123 **********0 24 ********** EGC AWS
```
**DNS Servers:** ***********, ************

## Supported Domain/Location Combinations

| Domain | Location  | Primary DNS    | Secondary DNS  |
|--------|-----------|----------------|----------------|
| ESI    | COLO      | ************   | *************  |
| ESI    | Nutley    | ************   | ************   |
| ESI    | Baltimore | ************   | ************   |
| ESI    | Raleigh   | *************  | *************  |
| ESI    | AWS       | ************   | ************   |
| EGC    | COLO      | ************   | *************  |
| EGC    | Nutley    | ************   | ************   |
| EGC    | AWS       | ***********    | ************   |

## Integration with Rundeck

### **Rundeck Job Options:**
```
DOMAIN_SHORT = ESI/EGC (Domain selection)
LOCATION = COLO/Nutley/Baltimore/Raleigh/AWS (Location selection)
IP_ADDRESS = **********0 (New IP address)
SUBNET_MASK = 24 (Subnet mask in CIDR)
GATEWAY = ********** (Default gateway)
```

### **Rundeck Step Configuration:**
```bash
python step2_update_ip_config.py ${option.TARGET_HOST} ${option.USERNAME} ${option.PASSWORD} ${option.IP_ADDRESS} ${option.SUBNET_MASK} ${option.GATEWAY} ${option.DOMAIN_SHORT} ${option.LOCATION}
```

The step now provides intelligent DNS server selection while maintaining the same level of IP configuration functionality!
