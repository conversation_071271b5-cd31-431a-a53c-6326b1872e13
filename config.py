#!/usr/bin/env python
"""
Configuration and common functions for server setup scripts
"""
import logging
import winrm
import traceback
from winrm.exceptions import WinRMTransportError

# DNS Mappings for different domains and locations
DNS_MAPPINGS = {
    ("ESI", "COLO"): ("************", "*************"),
    ("ESI", "Nutley"): ("************", "************"),
    ("ESI", "Baltimore"): ("************", "************"),
    ("ESI", "Raleigh"): ("*************", "*************"),
    ("EGC", "COLO"): ("************", "*************"),
    ("EGC", "Nutley"): ("************", "************"),
    ("ESI", "AWS"): ("************", "************"),
    ("EGC", "AWS"): ("***********", "************")
}

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("server_setup.log"),
        logging.StreamHandler()
    ]
)

def check_server_uptime(session):
    """
    Checks the uptime of the server using PowerShell.
    Returns True if the server is up, otherwise False.
    """
    ps_script = "(Get-CimInstance -ClassName Win32_OperatingSystem).LastBootUpTime"
    try:
        response = session.run_ps(ps_script)
        if response.status_code == 0:
            uptime = response.std_out.decode('utf-8').strip()
            print(f"Server is up. Uptime: {uptime}")
            return True
        else:
            print(f"Error checking server uptime: {response.std_err.decode('utf-8')}")
            return False
    except Exception as e:
        print(f"Exception during uptime check: {e}")
        return False

def client_login(host, username, password):
    """Establish WinRM session with the target server"""
    logging.info(f"Attempting to create WinRM session for {host}")
    session = winrm.Session(host, auth=(username, password), transport="ntlm")
    try:
        result = session.run_ps('Get-ExecutionPolicy')
        if result.status_code != 0:
            raise Exception(result.std_err.decode())

        original_policy = result.std_out.decode().strip()
        logging.info(f"Original Execution Policy on {host}: {original_policy}")

        if original_policy.lower() == 'restricted' or original_policy.lower() == 'undefined':
            # Temporarily allow script execution
            session.run_ps('Set-ExecutionPolicy RemoteSigned -Scope Process -Force')
            logging.info(f"Temporarily changed execution policy to RemoteSigned on {host}")

        logging.info(f"WinRM session established for {host}")
        return session, original_policy
    except Exception as e:
        logging.error(f"Failed to create WinRM session for {host}: {e}")
        return None, None

def run_command(session, cmd, step_name, host):
    """Execute PowerShell command via WinRM and return results"""
    logging.info(f"Executing '{step_name}' on {host}")
    
    try:
        result = session.run_ps(cmd)
        
        if result.status_code == 0:
            logging.info(f"Executed: {step_name} on {host}")
            return "Success", result.std_out.decode()
        elif result.status_code == 3010:
            return "Restart", result.std_out.decode()
        else:
            logging.error(f"Error in {step_name} on {host}: {result.std_err.decode()}")
            return "Failure", result.std_err.decode()

    except WinRMTransportError as e:
        logging.error(f"WinRMTransportError during '{step_name}' on {host}: {str(e)}")
        return "Failure", f"WinRMTransportError: {str(e)}"

    except Exception as e:
        logging.error(f"Unexpected error during '{step_name}' on {host}: {str(e)}")
        return "Failure", f"Unexpected error: {str(e)}"