#!/usr/bin/env python
"""
Step 11: Validate and Install Required Agents

This script validates if required agents are installed and installs missing agents based on environment:

On-prem Environment:
- Splunk UF agent
- Carbon Black
- Symantec End point AV

AWS Cloud Environment:
- Splunk UF agent
- Carbon Black
- Trend Micro AV
- Qualys scanner

Usage:
  python step11_validate_install_agents.py HOST USERNAME PASSWORD ENVIRONMENT
  
  Example:
  python step11_validate_install_agents.py ************* domain\\admin Password123 onprem
  python step11_validate_install_agents.py ************* domain\\admin Password123 aws
"""
import sys
import time
import logging
import winrm
import traceback

# Agent definitions for different environments
AGENT_DEFINITIONS = {
    "onprem": {
        "Splunk UF": {
            "service_name": "SplunkForwarder",
            "process_name": "splunkd.exe",
            "display_name": "Splunk Universal Forwarder"
        },
        "Carbon Black": {
            "service_name": "CarbonBlack",
            "process_name": "cb.exe",
            "display_name": "Carbon Black Response Sensor"
        },
        "Symantec Endpoint AV": {
            "service_name": "SepMasterService",
            "process_name": "ccSvcHst.exe",
            "display_name": "Symantec Endpoint Protection"
        }
    },
    "aws": {
        "Splunk UF": {
            "service_name": "SplunkForwarder",
            "process_name": "splunkd.exe",
            "display_name": "Splunk Universal Forwarder"
        },
        "Carbon Black": {
            "service_name": "CarbonBlack",
            "process_name": "cb.exe",
            "display_name": "Carbon Black Response Sensor"
        },
        "Trend Micro AV": {
            "service_name": "TMBMServer",
            "process_name": "TmListen.exe",
            "display_name": "Trend Micro Deep Security Agent"
        },
        "Qualys Scanner": {
            "service_name": "QualysAgent",
            "process_name": "QualysAgent.exe",
            "display_name": "Qualys Cloud Agent"
        }
    }
}

def check_agent_installed(session, agent_name, agent_info):
    """Check if a specific agent is installed and running"""
    try:
        logging.info(f"Checking {agent_name}...")
        
        # Check if service exists and is running
        service_check = f"""
        try {{
            $service = Get-Service -Name '{agent_info['service_name']}' -ErrorAction SilentlyContinue
            if ($service) {{
                Write-Output "Service: {agent_info['service_name']} - Status: $($service.Status)"
                if ($service.Status -eq 'Running') {{
                    Write-Output "INSTALLED_RUNNING"
                }} else {{
                    Write-Output "INSTALLED_STOPPED"
                }}
            }} else {{
                Write-Output "SERVICE_NOT_FOUND"
            }}
        }}
        catch {{
            Write-Output "SERVICE_CHECK_ERROR: $($_.Exception.Message)"
        }}
        """
        
        result = session.run_ps(service_check)
        output = result.std_out.decode('utf-8').strip()
        
        if "INSTALLED_RUNNING" in output:
            logging.info(f"✓ {agent_name} is installed and running")
            return "running"
        elif "INSTALLED_STOPPED" in output:
            logging.warning(f"⚠ {agent_name} is installed but not running")
            return "stopped"
        elif "SERVICE_NOT_FOUND" in output:
            logging.warning(f"✗ {agent_name} is not installed")
            return "not_installed"
        else:
            logging.error(f"? {agent_name} status check failed: {output}")
            return "unknown"
            
    except Exception as e:
        logging.error(f"Error checking {agent_name}: {e}")
        return "error"

def start_agent_service(session, agent_name, service_name):
    """Attempt to start an agent service"""
    try:
        logging.info(f"Attempting to start {agent_name} service...")
        
        start_command = f"""
        try {{
            Start-Service -Name '{service_name}' -ErrorAction Stop
            Write-Output "SERVICE_STARTED"
        }}
        catch {{
            Write-Output "START_FAILED: $($_.Exception.Message)"
        }}
        """
        
        result = session.run_ps(start_command)
        output = result.std_out.decode('utf-8').strip()
        
        if "SERVICE_STARTED" in output:
            logging.info(f"✓ {agent_name} service started successfully")
            return True
        else:
            logging.error(f"✗ Failed to start {agent_name} service: {output}")
            return False
            
    except Exception as e:
        logging.error(f"Error starting {agent_name} service: {e}")
        return False

def validate_and_install_agents(host, username, password, environment):
    """Validate and install required agents based on environment"""
    try:
        logging.info(f"Step 11: Validating and Installing Required Agents for {environment.upper()} environment")
        
        # Validate environment
        if environment.lower() not in AGENT_DEFINITIONS:
            logging.error(f"Invalid environment: {environment}. Must be 'onprem' or 'aws'")
            return False
        
        env_key = environment.lower()
        required_agents = AGENT_DEFINITIONS[env_key]
        
        logging.info(f"Required agents for {environment.upper()} environment:")
        for agent_name in required_agents.keys():
            logging.info(f"  - {agent_name}")
        
        # Connect to the server
        session = winrm.Session(host, auth=(username, password), transport='ntlm')
        logging.info(f"Connected to server: {host}")
        
        # Check each required agent
        agent_status = {}
        missing_agents = []
        stopped_agents = []
        
        for agent_name, agent_info in required_agents.items():
            status = check_agent_installed(session, agent_name, agent_info)
            agent_status[agent_name] = status
            
            if status == "not_installed":
                missing_agents.append(agent_name)
            elif status == "stopped":
                stopped_agents.append((agent_name, agent_info['service_name']))
        
        # Attempt to start stopped services
        for agent_name, service_name in stopped_agents:
            if start_agent_service(session, agent_name, service_name):
                agent_status[agent_name] = "running"
        
        # Report results
        logging.info("=" * 60)
        logging.info("AGENT VALIDATION RESULTS:")
        logging.info("=" * 60)
        
        all_agents_ok = True
        for agent_name, status in agent_status.items():
            if status == "running":
                logging.info(f"✓ {agent_name}: INSTALLED AND RUNNING")
            elif status == "stopped":
                logging.warning(f"⚠ {agent_name}: INSTALLED BUT STOPPED")
                all_agents_ok = False
            elif status == "not_installed":
                logging.error(f"✗ {agent_name}: NOT INSTALLED")
                all_agents_ok = False
            else:
                logging.error(f"? {agent_name}: STATUS UNKNOWN")
                all_agents_ok = False
        
        # Summary
        if missing_agents:
            logging.error("=" * 60)
            logging.error("MISSING AGENTS REQUIRE MANUAL INSTALLATION:")
            logging.error("=" * 60)
            for agent in missing_agents:
                logging.error(f"  - {agent}")
            logging.error("Please install missing agents manually and re-run validation.")
        
        if all_agents_ok:
            logging.info("=" * 60)
            logging.info("✓ ALL REQUIRED AGENTS ARE INSTALLED AND RUNNING")
            logging.info("=" * 60)
            return True
        else:
            logging.warning("=" * 60)
            logging.warning("⚠ SOME AGENTS REQUIRE ATTENTION")
            logging.warning("=" * 60)
            return False
            
    except Exception as e:
        logging.error("Error during agent validation and installation:")
        logging.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    # Check arguments
    if len(sys.argv) < 5:
        logging.error("Usage: python step11_validate_install_agents.py HOST USERNAME PASSWORD ENVIRONMENT")
        logging.error("Example: python step11_validate_install_agents.py ************* domain\\admin Password123 onprem")
        logging.error("Example: python step11_validate_install_agents.py ************* domain\\admin Password123 aws")
        logging.error("Environment options: onprem, aws")
        sys.exit(1)
    
    # Get arguments
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]
    environment = sys.argv[4]
    
    # Run the agent validation and installation
    success = validate_and_install_agents(host, username, password, environment)
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
