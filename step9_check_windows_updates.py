import sys
import logging
from config import client_login, run_command

def check_windows_updates(host, username, password):
    """Check for available Windows updates"""
    logging.info("Step 9: Checking for Windows Updates")

    # Login to the server
    session, original_policy = client_login(host, username, password)
    if not session:
        logging.error(f"Failed to connect to {host}. Check credentials and WinRM configuration.")
        return False

    # Check for available updates
    cmd = """
    $updateSession = New-Object -ComObject Microsoft.Update.Session
    $updateSearcher = $updateSession.CreateUpdateSearcher()
    $searchResult = $updateSearcher.Search("IsInstalled=0 and Type='Software'")
    $searchResult.Updates.Count
    """
    status, update_count = run_command(session, cmd, "Check Available Updates", host)

    if status == "Success":
        update_count = update_count.strip()
        logging.info(f"Found {update_count} available updates")

        if update_count != "0":
            logging.warning("Note: Installing updates requires manual intervention")
            logging.info("Please use Windows Update in Settings to install updates")
        else:
            logging.info("No updates available. System is up to date.")

        return True
    else:
        logging.error(f"Failed to check for updates: {update_count}")
        return False

if __name__ == "__main__":
    # Check arguments
    if len(sys.argv) < 4:
        logging.error("Usage: python step9_check_windows_updates.py HOST USERNAME PASSWORD")
        logging.error("Example: python step9_check_windows_updates.py ************* domain\\admin Password123")
        sys.exit(1)

    # Get arguments
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]

    # Run the Windows updates check
    success = check_windows_updates(host, username, password)

    # Exit with appropriate code
    sys.exit(0 if success else 1)
