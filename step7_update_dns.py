#!/usr/bin/env python
"""
Step 7: Update DNS Settings

This script updates DNS settings based on domain and location using PowerShell commands executed via WinRM.

Usage:
  python step7_update_dns.py HOST USERNAME PASSWORD DOMAIN LOCATION
  
  Example:
  python step7_update_dns.py ************* domain\\admin Password123 ESI COLO
"""
import sys
import time
import logging
import winrm
from config import DNS_MAPPINGS, check_server_uptime

def update_dns(host, username, password, domain, location):
    """Update DNS settings based on domain and location"""
    logging.info(f"Step 7: Updating DNS Settings for Domain: {domain}, Location: {location}")

    try:
        logging.info("Waiting for 5 minutes before attempting connection to the server...")
        time.sleep(300)

        session = winrm.Session(host, auth=(username, password), transport='ntlm')

        # Check server uptime before proceeding
        if not check_server_uptime(session):
            logging.error("Server not responding")
            return False

        # Proceed with DNS update
        dns_settings = DNS_MAPPINGS.get((domain, location))
        if not dns_settings:
            logging.error(f"Invalid domain ({domain}) or location ({location}) provided.")
            logging.info("Valid combinations are:")
            for key in DNS_MAPPINGS.keys():
                logging.info(f"  - Domain: {key[0]}, Location: {key[1]}")
            return False

        primary_dns, secondary_dns = dns_settings
        ps_script = f"""
        $adapter = Get-NetAdapter | Where-Object {{ $_.Status -eq "Up" }}
        Set-DnsClientServerAddress -InterfaceAlias $adapter.Name -ServerAddresses "{primary_dns}", "{secondary_dns}"
        """
        file_command = session.run_ps(ps_script)
        status_code = file_command.status_code

        if status_code == 0:
            logging.info(f"DNS updated successfully for Domain: {domain}, Location: {location}")
            logging.info(f"Primary DNS: {primary_dns}")
            logging.info(f"Secondary DNS: {secondary_dns}")
            return True
        else:
            logging.error(f"Error while updating DNS for Domain: {domain}, Location: {location}.")
            logging.error(file_command.std_err.decode('utf-8') if file_command.std_err else "No error details available.")
            return False

    except Exception as e:
        import traceback
        logging.error("Automation failed to update DNS on the impacted server. Please check this issue manually.")
        logging.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    # Check arguments
    if len(sys.argv) < 6:
        logging.error("Usage: python step7_update_dns.py HOST USERNAME PASSWORD DOMAIN_SHORT LOCATION")
        logging.error("Example: python step7_update_dns.py ************* domain\\admin Password123 ESI COLO")
        sys.exit(1)

    # Get arguments
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]
    domain_short = sys.argv[4]
    location = sys.argv[5]

    # Run the DNS update operation
    success = update_dns(host, username, password, domain_short, location)

    # Exit with appropriate code
    sys.exit(0 if success else 1)
