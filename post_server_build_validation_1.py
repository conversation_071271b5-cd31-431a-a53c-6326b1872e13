#!/usr/bin/env python
"""
Post Server Build Validation 1

This script validates server configuration according to the build requirements.
It checks hostname, IP configuration, service status, domain join status,
installed agents, and other required configurations.

Usage:
  python post_server_build_validation_1.py         # Run all checks
  python post_server_build_validation_1.py 1 3 5   # Run only checks 1, 3, and 5

Validation Steps:
  1. Hostname
  2. IP Configuration
  3. IPv6 Status
  4. Print Spooler Service
  5. Domain Join Status
  6. Computer OU Placement (WCL/Server OU)
  7. DNS Settings
  8. Windows Activation
  9. Required Agents
  10. Windows Updates
  11. Security Group Membership
"""
import os
import subprocess
import socket
import platform
import ctypes
import re
import sys
from datetime import datetime

def is_admin():
    """Check if the script is running with administrator privileges"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin() != 0
    except:
        return False

def run_command(command):
    """Run a command and return its output"""
    try:
        result = subprocess.run(command, shell=True, check=True, 
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                               universal_newlines=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        return f"Error: {e.stderr.strip()}"

def check_hostname():
    """Check the current hostname"""
    hostname = socket.gethostname()
    print(f"1. Current Hostname: {hostname}")
    print("   Note: To rename the server, use System settings or run 'sysdm.cpl'")

def check_ip_config():
    """Check IP configuration and DNS settings"""
    print("\n2. IP Configuration:")
    ipconfig = run_command("ipconfig /all")
    
    # Extract and display IP address, subnet mask, gateway, and DNS servers
    ip_match = re.search(r"IPv4 Address[.\s]+: ([^\s]+)", ipconfig)
    subnet_match = re.search(r"Subnet Mask[.\s]+: ([^\s]+)", ipconfig)
    gateway_match = re.search(r"Default Gateway[.\s]+: ([^\s]+)", ipconfig)
    dns_servers = re.findall(r"DNS Servers[.\s]+: ([^\s]+)", ipconfig)
    
    if ip_match:
        print(f"   IP Address: {ip_match.group(1)}")
    if subnet_match:
        print(f"   Subnet Mask: {subnet_match.group(1)}")
    if gateway_match:
        print(f"   Default Gateway: {gateway_match.group(1)}")
    
    print("   DNS Servers:")
    for i, dns in enumerate(dns_servers, 1):
        print(f"   {i}. {dns}")
    
    # Check if DNS servers match the expected values based on domain and location
    print("\n   Expected DNS Servers by Domain and Location:")
    print("   ESI Domain:")
    print("     COLO: ************, ************")
    print("     Nutley: ***********, ***********")
    print("     Baltimore: ***********, ***********")
    print("     Raleigh: ************, *************")
    print("     AWS: ************, ************")
    print("   EGC Domain:")
    print("     COLO: *************, *************")
    print("     Nutley: ***********, ***********")
    print("     AWS: ***********, ************")

def check_ipv6_status():
    """Check if IPv6 is disabled on network adapters"""
    print("\n3. IPv6 Status:")
    ipv6_check = run_command("netsh interface ipv6 show interface")
    if "disabled" in ipv6_check.lower():
        print("   IPv6 is disabled")
    else:
        print("   IPv6 may be enabled. Please disable IPv6 on all network adapters.")
        print("   To disable: Open network adapter properties and uncheck 'Internet Protocol Version 6 (TCP/IPv6)'")

def check_print_spooler():
    """Check if Print Spooler service is disabled"""
    print("\n4. Print Spooler Service Status:")
    spooler_status = run_command("sc query Spooler")
    
    if "STOPPED" in spooler_status:
        print("   Print Spooler service is stopped")
    else:
        print("   Print Spooler service is running. It should be stopped.")
    
    spooler_config = run_command("sc qc Spooler | findstr START_TYPE")
    if "DISABLED" in spooler_config:
        print("   Print Spooler service is disabled")
    else:
        print("   Print Spooler service is not disabled. It should be disabled.")
        print("   To disable: Run 'services.msc', find Print Spooler, stop it and set startup type to Disabled")

def check_domain_join():
    """Check if server is joined to the domain"""
    print("\n5. Domain Join Status:")
    domain_info = run_command("systeminfo | findstr /B /C:\"Domain\"")
    print(f"   {domain_info}")
    print("   Expected domains: ESI.US.EISAI.LOCAL or EGC.US.EISAI.LOCAL")

def check_computer_ou():
    """Remind to check computer OU placement"""
    print("\n6. Computer Object OU Placement:")
    print("   Manual check required: Verify the computer object is in WCL/Server OU")
    print("   Steps: Login to esidcprod03 Domain Controller, open ADUC console")
    print("   Move the computer object from /Computer OU to WCL/Server OU if needed")

def check_dns_settings():
    """Check DNS settings"""
    print("\n7. DNS Settings:")
    dns_info = run_command("ipconfig /all | findstr /C:\"DNS Servers\"")
    print(f"   Current DNS Servers: {dns_info}")
    print("   Expected DNS Servers by Domain and Location:")
    print("   ESI Domain:")
    print("     COLO: ************, *************")
    print("     Nutley: ************, ************")
    print("     Baltimore: ************, ************")
    print("     Raleigh: *************, *************")
    print("     AWS: ************, ************")
    print("   EGC Domain:")
    print("     COLO: ************, *************")
    print("     Nutley: ************, ************")
    print("     AWS: ***********, ************")

def check_windows_activation():
    """Check Windows activation status"""
    print("\n8. Windows Activation Status:")
    activation_status = run_command("slmgr /dli")

    if "License Status: Licensed" in activation_status:
        print("   Windows is activated")
    else:
        print("   Windows may not be activated. Run 'slmgr /ato' to activate")
        print(f"   Current status: {activation_status}")

def check_required_agents():
    """Check for required agents"""
    print("\n9. Required Agents:")
    
    # Check for Splunk Universal Forwarder
    splunk_path = "C:\\Program Files\\SplunkUniversalForwarder"
    if os.path.exists(splunk_path):
        print("   ✓ Splunk UF agent installed")
    else:
        print("   ✗ Splunk UF agent not found")
    
    # Check for Carbon Black
    cb_path = "C:\\Program Files\\Confer"
    if os.path.exists(cb_path):
        print("   ✓ Carbon Black installed")
    else:
        print("   ✗ Carbon Black not found")
    
    # Check for Symantec Endpoint Protection
    sep_path = "C:\\Program Files (x86)\\Symantec\\Symantec Endpoint Protection"
    if os.path.exists(sep_path):
        print("   ✓ Symantec Endpoint Protection installed")
    else:
        print("   ✗ Symantec Endpoint Protection not found")
    
    # Check for Trend Micro
    trend_path = "C:\\Program Files (x86)\\Trend Micro"
    if os.path.exists(trend_path):
        print("   ✓ Trend Micro installed")
    else:
        print("   ✗ Trend Micro not found")
    
    # Check for Qualys
    qualys_path = "C:\\Program Files\\Qualys"
    if os.path.exists(qualys_path):
        print("   ✓ Qualys scanner installed")
    else:
        print("   ✗ Qualys scanner not found")

def check_windows_updates():
    """Check Windows update status"""
    print("\n10. Windows Updates Status:")
    print("   Last check time for Windows updates:")
    update_history = run_command("powershell -command \"Get-HotFix | Sort-Object -Property InstalledOn -Descending | Select-Object -First 5 | Format-Table -Property HotFixID, Description, InstalledOn\"")
    print(f"   Recent updates:\n{update_history}")

def check_security_groups():
    """Remind to check security group membership"""
    print("\n11. Security Group Membership:")
    domain_info = run_command("systeminfo | findstr /B /C:\"Domain\"")
    
    if "ESI.US.EISAI.LOCAL" in domain_info:
        print("   Server is in ESI domain. Add to: UG_ESI_Mamoru_Wincollector")
    elif "EGC.US.EISAI.LOCAL" in domain_info:
        print("   Server is in EGC domain. Add to: UG_EGC_Mamoru_Wincollector")
    else:
        print("   Domain not recognized. Please check domain and add to appropriate group:")
        print("   - ESI Domain: UG_ESI_Mamoru_Wincollector")
        print("   - EGC Domain: UG_EGC_Mamoru_Wincollector")

def main():
    """Main function to run all validation checks"""
    if not is_admin():
        print("This script requires administrator privileges. Please run as administrator.")
        return

    print("=" * 80)
    print(f"POST SERVER BUILD VALIDATION 1 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Server: {socket.gethostname()}")
    print("=" * 80)

    # Check if command-line arguments are provided
    if len(sys.argv) > 1:
        # Run only the specified checks
        for arg in sys.argv[1:]:
            try:
                check_num = int(arg)
                if check_num == 1:
                    check_hostname()
                elif check_num == 2:
                    check_ip_config()
                elif check_num == 3:
                    check_ipv6_status()
                elif check_num == 4:
                    check_print_spooler()
                elif check_num == 5:
                    check_domain_join()
                elif check_num == 6:
                    check_computer_ou()
                elif check_num == 7:
                    check_dns_settings()
                elif check_num == 8:
                    check_windows_activation()
                elif check_num == 9:
                    check_required_agents()
                elif check_num == 10:
                    check_windows_updates()
                elif check_num == 11:
                    check_security_groups()
                else:
                    print(f"Invalid check number: {check_num}")
            except ValueError:
                print(f"Invalid argument: {arg}. Please use numbers 1-11.")
    else:
        # Run all checks if no arguments provided
        check_hostname()
        check_ip_config()
        check_ipv6_status()
        check_print_spooler()
        check_domain_join()
        check_computer_ou()
        check_dns_settings()
        check_windows_activation()
        check_required_agents()
        check_windows_updates()
        check_security_groups()

    print("\nValidation complete. Please review the results and address any issues.")
    print("=" * 80)

if __name__ == "__main__":
    main()