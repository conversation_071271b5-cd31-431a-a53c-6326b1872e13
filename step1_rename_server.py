import sys
import logging
from config import client_login, run_command

def rename_server(host, username, password, new_hostname):
    """Rename the server to the specified hostname"""
    logging.info(f"Step 1: Renaming server to: {new_hostname}")

    # Login to the server
    session, original_policy = client_login(host, username, password)
    if not session:
        logging.error(f"Failed to connect to {host}. Check credentials and WinRM configuration.")
        return False

    # Get current hostname
    cmd = "$env:COMPUTERNAME"
    status, current_hostname = run_command(session, cmd, "Get Current Hostname", host)

    if status != "Success":
        logging.error(f"Failed to get current hostname: {current_hostname}")
        return False

    current_hostname = current_hostname.strip()

    if current_hostname.upper() == new_hostname.upper():
        logging.info(f"Server is already named {new_hostname}. No change needed.")
        return True

    # Rename the computer
    cmd = f"Rename-Computer -NewName '{new_hostname}' -Force"
    status, output = run_command(session, cmd, "Rename Server", host)

    if status == "Success":
        logging.info(f"Server renamed to {new_hostname}. A restart will be required.")
        return True
    else:
        logging.error(f"Failed to rename server: {output}")
        return False

if __name__ == "__main__":
    # Check arguments
    if len(sys.argv) < 5:
        logging.error("Usage: python step1_rename_server.py HOST USERNAME PASSWORD NEW_HOSTNAME")
        logging.error("Example: python step1_rename_server.py ************* domain\\admin Password123 NEWSERVER01")
        sys.exit(1)

    # Get arguments
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]
    new_hostname = sys.argv[4]

    # Run the rename operation
    success = rename_server(host, username, password, new_hostname)

    # Exit with appropriate code
    sys.exit(0 if success else 1)