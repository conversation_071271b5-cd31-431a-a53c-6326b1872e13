# DNS Logic Moved from Step 7 to Step 2

## Overview
The DNS update functionality has been moved from Step 7 to Step 2. Step 2 now handles DNS updates based on domain and location, while Step 7 is now a placeholder that redirects users to Step 2.

## Changes Made

### **1. Step 2 (step2_update_ip_config.py) - Now DNS Update Script**

#### **Updated to DNS-Only Functionality:**
- **Removed IP configuration logic**
- **Added DNS update logic from Step 7**
- **Renamed function** from `update_ip_config()` to `update_dns()`
- **Updated documentation** to reflect DNS-only functionality

#### **New Function Signature:**
```python
def update_dns(host, username, password, domain, location):
    """Update DNS settings based on domain and location"""
```

#### **Features:**
- ✅ **5-minute delay** before attempting connection
- ✅ **Server uptime check** before proceeding
- ✅ **Automatic DNS server selection** based on domain/location
- ✅ **Comprehensive error handling** and logging
- ✅ **PowerShell-based DNS configuration**

### **2. Step 7 (step7_update_dns.py) - Now Placeholder**

#### **Converted to Placeholder:**
- **Removed DNS update logic**
- **Added placeholder function** that redirects to Step 2
- **Updated documentation** to indicate functionality moved

#### **Placeholder Function:**
```python
def step7_placeholder():
    """Placeholder function that redirects to Step 2"""
    logging.warning("Step 7: DNS update functionality has been moved to Step 2")
    logging.info("Please use step2_update_dns.py instead")
    return True
```

## Updated Usage

### **Step 2 - DNS Update (New)**
```bash
python step2_update_ip_config.py HOST USERNAME PASSWORD DOMAIN LOCATION

# Example:
python step2_update_ip_config.py ************* domain\admin Password123 ESI COLO
```

#### **Arguments:**
1. **HOST** - Target server IP/hostname
2. **USERNAME** - WinRM username
3. **PASSWORD** - WinRM password
4. **DOMAIN** - Domain (ESI or EGC)
5. **LOCATION** - Location (COLO, Nutley, Baltimore, Raleigh, AWS)

### **Step 7 - Placeholder (Updated)**
```bash
python step7_update_dns.py
# Will display message redirecting to Step 2
```

## DNS Mappings (Now in Step 2)

The DNS mappings are now defined in Step 2:

```python
DNS_MAPPINGS = {
    ("ESI", "COLO"): ("************", "*************"),
    ("ESI", "Nutley"): ("************", "************"),
    ("ESI", "Baltimore"): ("************", "************"),
    ("ESI", "Raleigh"): ("*************", "*************"),
    ("EGC", "COLO"): ("************", "*************"),
    ("EGC", "Nutley"): ("************", "************"),
    ("ESI", "AWS"): ("************", "************"),
    ("EGC", "AWS"): ("***********", "************")
}
```

## PowerShell Script (Now in Step 2)

The DNS configuration PowerShell script:

```powershell
$adapter = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }
Set-DnsClientServerAddress -InterfaceAlias $adapter.Name -ServerAddresses "PRIMARY_DNS", "SECONDARY_DNS"
```

## Benefits of the Move

### **1. Logical Consolidation**
- ✅ DNS configuration is now in Step 2 (early in the process)
- ✅ Reduces the number of active steps
- ✅ Simplifies the overall workflow

### **2. Better Sequencing**
- ✅ DNS is configured early in the server setup process
- ✅ Ensures proper name resolution for subsequent steps
- ✅ Follows logical network configuration order

### **3. Reduced Complexity**
- ✅ One less step to manage
- ✅ Consolidated network-related configuration
- ✅ Simplified Rundeck job configuration

## Updated Server Setup Sequence

### **New Sequence (Step 7 functionality moved to Step 2):**
1. **Step 1**: Rename Server
2. **Step 2**: Update DNS Settings ← **DNS logic now here**
3. **Step 3**: Disable IPv6
4. **Step 4**: Disable Print Spooler
5. **Step 5**: Join Domain
6. **Step 6**: Move Computer to WCL/Server OU
7. **Step 7**: Placeholder (redirects to Step 2) ← **Now placeholder**
8. **Step 8**: Activate Windows
9. **Step 9**: Check Windows Updates

## Usage Examples

### **ESI Domain - COLO Location:**
```bash
python step2_update_ip_config.py ************* domain\admin Password123 ESI COLO
```
**Result:** DNS servers set to ************, *************

### **EGC Domain - AWS Location:**
```bash
python step2_update_ip_config.py ************* domain\admin Password123 EGC AWS
```
**Result:** DNS servers set to ***********, ************

### **ESI Domain - Nutley Location:**
```bash
python step2_update_ip_config.py ************* domain\admin Password123 ESI Nutley
```
**Result:** DNS servers set to ************, ************

## Integration with Rundeck

### **Updated Rundeck Job Configuration:**

#### **Step 2 - DNS Update:**
```bash
python step2_update_ip_config.py ${option.TARGET_HOST} ${option.USERNAME} ${option.PASSWORD} ${option.DOMAIN_SHORT} ${option.LOCATION}
```

#### **Step 7 - Skip or Remove:**
- **Option 1**: Skip Step 7 entirely in Rundeck jobs
- **Option 2**: Keep as placeholder for workflow consistency

### **Rundeck Job Options (Updated):**
```
# Remove IP-related options, keep DNS-related options
DOMAIN_SHORT = ESI/EGC (Domain selection)
LOCATION = COLO/Nutley/Baltimore/Raleigh/AWS (Location selection)
```

## Migration Notes

### **For Existing Rundeck Jobs:**
1. **Update Step 2** to use new DNS-only parameters
2. **Remove or skip Step 7** from job workflows
3. **Update job options** to remove IP configuration parameters
4. **Test DNS functionality** in Step 2

### **For Manual Execution:**
1. **Use Step 2** for DNS updates instead of Step 7
2. **Update any scripts** that call Step 7 to call Step 2 instead
3. **Update documentation** to reflect the new step sequence

The DNS functionality is now consolidated in Step 2, making the server setup process more streamlined and logical!
