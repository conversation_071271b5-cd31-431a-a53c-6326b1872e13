import sys
import time
import logging
import winrm
import traceback

# DNS Mappings for different domains and locations
DNS_MAPPINGS = {
    ("ESI", "COLO"): ("************", "*************"),
    ("E<PERSON>", "Nutley"): ("************", "************"),
    ("ESI", "Baltimore"): ("************", "************"),
    ("ESI", "Raleigh"): ("*************", "*************"),
    ("EGC", "COLO"): ("************", "*************"),
    ("EGC", "Nutley"): ("************", "************"),
    ("ESI", "AWS"): ("************", "************"),
    ("EGC", "AWS"): ("***********", "************")
}

def check_server_uptime(session):
    """
    Checks the uptime of the server using PowerShell.
    Returns True if the server is up, otherwise False.
    """
    ps_script = "(Get-CimInstance -ClassName Win32_OperatingSystem).LastBootUpTime"
    try:
        response = session.run_ps(ps_script)
        if response.status_code == 0:
            uptime = response.std_out.decode('utf-8').strip()
            logging.info(f"Server is up. Uptime: {uptime}")
            return True
        else:
            logging.error(f"Error checking server uptime: {response.std_err.decode('utf-8')}")
            return False
    except Exception as e:
        logging.error(f"Exception during uptime check: {e}")
        return False

def update_ip_and_dns(host, username, password, ip_address, subnet_mask, gateway, domain, location):
    """Update IP configuration and DNS settings based on domain and location"""
    try:
        logging.info("Step 2: Updating IP Configuration and DNS Settings")

        # Wait for 5 minutes before attempting connection
        logging.info("Waiting for 5 minutes before attempting connection to the server...")
        time.sleep(300)

        # Create WinRM session
        session = winrm.Session(host, auth=(username, password), transport='ntlm')

        # Check server uptime before proceeding
        if not check_server_uptime(session):
            logging.error("Server not responding")
            return False

        # Get DNS settings based on domain and location
        dns_settings = DNS_MAPPINGS.get((domain, location))
        if not dns_settings:
            logging.error(f"Invalid domain ({domain}) or location ({location}) provided.")
            logging.info("Valid combinations are:")
            for key in DNS_MAPPINGS.keys():
                logging.info(f"  - Domain: {key[0]}, Location: {key[1]}")
            return False

        primary_dns, secondary_dns = dns_settings

        # Combined PowerShell script for IP and DNS configuration
        ps_script = f"""
        # Get the active network adapter
        $adapter = Get-NetAdapter | Where-Object {{ $_.Status -eq "Up" }} | Select-Object -First 1
        $interfaceIndex = $adapter.InterfaceIndex
        $interfaceAlias = $adapter.Name

        Write-Output "Configuring network adapter: $interfaceAlias (Index: $interfaceIndex)"

        # Remove existing IP configuration
        Remove-NetIPAddress -InterfaceIndex $interfaceIndex -Confirm:$false -ErrorAction SilentlyContinue
        Remove-NetRoute -InterfaceIndex $interfaceIndex -Confirm:$false -ErrorAction SilentlyContinue

        # Set new IP configuration
        New-NetIPAddress -InterfaceIndex $interfaceIndex -IPAddress '{ip_address}' -PrefixLength {subnet_mask} -DefaultGateway '{gateway}'

        # Set DNS servers
        Set-DnsClientServerAddress -InterfaceAlias $interfaceAlias -ServerAddresses "{primary_dns}", "{secondary_dns}"

        Write-Output "Network configuration completed successfully"
        Write-Output "IP Address: {ip_address}/{subnet_mask}"
        Write-Output "Gateway: {gateway}"
        Write-Output "DNS Servers: {primary_dns}, {secondary_dns}"
        """

        logging.info(f"Configuring network for Domain: {domain}, Location: {location}")
        command = session.run_ps(ps_script)
        status_code = command.status_code

        if status_code == 0:
            output = command.std_out.decode('utf-8').strip()
            logging.info("Network configuration completed successfully")
            logging.info(f"IP Address set to: {ip_address}/{subnet_mask}")
            logging.info(f"Default Gateway set to: {gateway}")
            logging.info(f"DNS Servers set to: {primary_dns}, {secondary_dns}")
            logging.info(f"DNS updated successfully for Domain: {domain}, Location: {location}")
            if output:
                logging.info(f"PowerShell output: {output}")
            return True
        else:
            logging.error(f"Failed to update network configuration. Status code: {status_code}")
            if command.std_err:
                error_details = command.std_err.decode('utf-8')
                logging.error(f"Error details: {error_details}")
            return False

    except Exception as e:
        logging.error("Automation failed to update network configuration on the server. Please check this issue manually.")
        logging.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    # Check arguments
    if len(sys.argv) < 9:
        logging.error("Usage: python step2_update_ip_config.py HOST USERNAME PASSWORD IP_ADDRESS SUBNET_MASK GATEWAY DOMAIN LOCATION")
        logging.error("Example: python step2_update_ip_config.py ************* domain\\admin Password123 **********0 24 ********** ESI COLO")
        sys.exit(1)

    # Get arguments
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]
    ip_address = sys.argv[4]
    subnet_mask = sys.argv[5]
    gateway = sys.argv[6]
    domain = sys.argv[7]
    location = sys.argv[8]

    # Run the IP configuration update
    success = update_ip_config(host, username, password, ip_address, subnet_mask, gateway, domain, location)

    # Exit with appropriate code
    sys.exit(0 if success else 1)