#!/usr/bin/env python
"""
Step 2: Update IP Configuration

This script updates the IP configuration of a Windows server using PowerShell commands executed via WinRM.

Usage:
  python step2_update_ip_config.py HOST USERNAME PASSWORD IP_ADDRESS SUBNET_MASK GATEWAY DNS_SERVERS
  
  Example:
  python step2_update_ip_config.py ************* domain\\admin Password123 **********0 24 ********** "************,************"
"""
import sys
import logging
from config import client_login, run_command
from global_args import get_step2_args

def update_ip_config(host, username, password, ip_address, subnet_mask, gateway, dns_servers):
    """Update IP configuration including IP address, subnet mask, gateway, and DNS servers"""
    logging.info("Step 2: Updating IP Configuration")

    # Login to the server
    session, original_policy = client_login(host, username, password)
    if not session:
        logging.error(f"Failed to connect to {host}. Check credentials and WinRM configuration.")
        return False

    # Get network adapter index
    cmd = "Get-NetAdapter | Where-Object {$_.Status -eq 'Up'} | Select-Object -First 1 | ForEach-Object { $_.InterfaceIndex }"
    status, adapter_index = run_command(session, cmd, "Get Network Adapter Index", host)

    if status != "Success" or not adapter_index.strip():
        logging.error("Failed to get network adapter index")
        return False

    adapter_index = adapter_index.strip()

    # Set IP address, subnet mask, and gateway
    cmd = f"New-NetIPAddress -InterfaceIndex {adapter_index} -IPAddress '{ip_address}' -PrefixLength {subnet_mask} -DefaultGateway '{gateway}' -ErrorAction SilentlyContinue"
    status, output = run_command(session, cmd, "Set IP Address", host)

    if "Failure" in status:
        # Try to remove existing IP and set again
        cmd = f"Remove-NetIPAddress -InterfaceIndex {adapter_index} -Confirm:$false -ErrorAction SilentlyContinue"
        run_command(session, cmd, "Remove Existing IP", host)
        
        cmd = f"New-NetIPAddress -InterfaceIndex {adapter_index} -IPAddress '{ip_address}' -PrefixLength {subnet_mask} -DefaultGateway '{gateway}'"
        status, output = run_command(session, cmd, "Set IP Address (Retry)", host)
    
    # Set DNS servers
    dns_list = dns_servers.split(',')
    cmd = f"Set-DnsClientServerAddress -InterfaceIndex {adapter_index} -ServerAddresses {','.join([f"'{dns}'" for dns in dns_list])}"
    dns_status, dns_output = run_command(session, cmd, "Set DNS Servers", host)
    
    if status == "Success" and dns_status == "Success":
        logging.info(f"IP Address set to: {ip_address}")
        logging.info(f"Subnet Mask set to: {subnet_mask}")
        logging.info(f"Default Gateway set to: {gateway}")
        logging.info(f"DNS Servers set to: {dns_servers}")
        return True
    else:
        logging.error(f"Failed to update IP configuration: {output}")
        logging.error(f"DNS configuration status: {dns_output}")
        return False

if __name__ == "__main__":
    # Get standardized arguments
    args = get_step2_args()

    # Run the IP configuration update
    success = update_ip_config(args['host'], args['username'], args['password'],
                             args['ip_address'], args['subnet_mask'], args['gateway'], args['dns_servers'])

    # Exit with appropriate code
    sys.exit(0 if success else 1)