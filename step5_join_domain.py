#!/usr/bin/env python
"""
Step 5: Join Domain

This script joins a Windows server to a domain using PowerShell commands executed via WinRM.

Usage:
  python step5_join_domain.py HOST USERNAME PASSWORD DOMAIN DOMAIN_USERNAME DOMAIN_PASSWORD
  
  Example:
  python step5_join_domain.py ************* domain\\admin Password123 ESI.US.EISAI.LOCAL domain\\admin DomainPass123
"""
import sys
import logging
from config import client_login, run_command

def join_domain(host, username, password, domain, domain_username, domain_password):
    """Join server to domain"""
    logging.info(f"Step 5: Joining server to domain: {domain}")

    # Login to the server
    session, original_policy = client_login(host, username, password)
    if not session:
        logging.error(f"Failed to connect to {host}. Check credentials and WinRM configuration.")
        return False

    # Check current domain
    cmd = "(Get-WmiObject -Class Win32_ComputerSystem).Domain"
    status, current_domain = run_command(session, cmd, "Get Current Domain", host)

    if status == "Success" and current_domain.strip().upper() == domain.upper():
        logging.info(f"Server is already joined to domain {domain}")
        return True

    # Join the domain
    cmd = f"""
    $securePassword = ConvertTo-SecureString -String '{domain_password}' -AsPlainText -Force
    $credential = New-Object System.Management.Automation.PSCredential('{domain_username}', $securePassword)
    Add-Computer -DomainName '{domain}' -Credential $credential -Force
    """
    status, output = run_command(session, cmd, "Join Domain", host)

    if status == "Success" or status == "Restart":
        logging.info(f"Server has been joined to domain {domain}. A restart is required.")
        return True
    else:
        logging.error(f"Failed to join domain: {output}")
        return False

if __name__ == "__main__":
    # Check arguments
    if len(sys.argv) < 7:
        logging.error("Usage: python step5_join_domain.py HOST USERNAME PASSWORD DOMAIN DOMAIN_USERNAME DOMAIN_PASSWORD")
        logging.error("Example: python step5_join_domain.py ************* domain\\admin Password123 ESI.US.EISAI.LOCAL domain\\admin DomainPass123")
        sys.exit(1)

    # Get arguments
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]
    domain = sys.argv[4]
    domain_username = sys.argv[5]
    domain_password = sys.argv[6]

    if domain == "ESI":
        domain_name = "ESI.US.EISAI.LOCAL"
    elif domain == "EGC":
        domain_name = "EGC.US.EISAI.LOCAL"

    
    # Run the domain join operation
    success = join_domain(host, username, password, domain_name, domain_username, domain_password)

    # Exit with appropriate code
    sys.exit(0 if success else 1)