#!/usr/bin/env python
"""
Step 8: Activate Windows

This script activates Windows on a server using PowerShell commands executed via WinRM.

Usage:
  python step8_activate_windows.py HOST USERNAME PASSWORD
  
  Example:
  python step8_activate_windows.py ************* domain\\admin Password123
"""
import sys
import logging
from config import client_login, run_command

def activate_windows(host, username, password):
    """Activate Windows"""
    logging.info("Step 8: Activating Windows")

    # Login to the server
    session, original_policy = client_login(host, username, password)
    if not session:
        logging.error(f"Failed to connect to {host}. Check credentials and WinRM configuration.")
        return False

    # Run Windows activation
    cmd = "cscript C:\\Windows\\System32\\slmgr.vbs /ato"
    status, output = run_command(session, cmd, "Activate Windows", host)

    if status == "Success":
        logging.info("Windows activation has been initiated")

        # Check activation status
        cmd = "cscript C:\\Windows\\System32\\slmgr.vbs /dli"
        status, check_output = run_command(session, cmd, "Check Activation Status", host)

        if "License Status: Licensed" in check_output:
            logging.info("Windows is now activated")
        else:
            logging.warning("Windows activation may not be complete. Please check manually.")

        return True
    else:
        logging.error(f"Failed to activate Windows: {output}")
        return False

if __name__ == "__main__":
    # Check arguments
    if len(sys.argv) < 4:
        logging.error("Usage: python step8_activate_windows.py HOST USERNAME PASSWORD")
        logging.error("Example: python step8_activate_windows.py ************* domain\\admin Password123")
        sys.exit(1)

    # Get arguments
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]

    # Run the Windows activation
    success = activate_windows(host, username, password)

    # Exit with appropriate code
    sys.exit(0 if success else 1)
