import sys
import time
import logging
import winrm
import traceback

# DNS Mappings for different domains and locations
DNS_MAPPINGS = {
    ("ESI", "COLO"): ("************", "*************"),
    ("E<PERSON>", "Nutley"): ("************", "************"),
    ("ESI", "Baltimore"): ("************", "************"),
    ("ESI", "Raleigh"): ("*************", "*************"),
    ("EGC", "COLO"): ("************", "*************"),
    ("EGC", "Nutley"): ("************", "************"),
    ("ESI", "AWS"): ("************", "************"),
    ("EGC", "AWS"): ("***********", "************")
}

def check_server_uptime(session):
    """
    Checks the uptime of the server using PowerShell.
    Returns True if the server is up, otherwise False.
    """
    ps_script = "(Get-CimInstance -ClassName Win32_OperatingSystem).LastBootUpTime"
    try:
        response = session.run_ps(ps_script)
        if response.status_code == 0:
            uptime = response.std_out.decode('utf-8').strip()
            logging.info(f"Server is up. Uptime: {uptime}")
            return True
        else:
            logging.error(f"Error checking server uptime: {response.std_err.decode('utf-8')}")
            return False
    except Exception as e:
        logging.error(f"Exception during uptime check: {e}")
        return False

def update_ip_config(host, username, password, ip_address, subnet_mask, gateway, domain, location):
    """Update IP configuration including IP address, subnet mask, gateway, and DNS servers"""
    try:
        logging.info("Step 2: Updating IP Configuration")

        # Wait for 5 minutes before attempting connection
        logging.info("Waiting for 5 minutes before attempting connection to the server...")
        time.sleep(300)

        # Create WinRM session
        session = winrm.Session(host, auth=(username, password), transport='ntlm')

        # Check server uptime before proceeding
        if not check_server_uptime(session):
            logging.error("Server not responding")
            return False

        # Get DNS settings based on domain and location
        dns_settings = DNS_MAPPINGS.get((domain, location))
        if not dns_settings:
            logging.error(f"Invalid domain ({domain}) or location ({location}) provided.")
            logging.info("Valid combinations are:")
            for key in DNS_MAPPINGS.keys():
                logging.info(f"  - Domain: {key[0]}, Location: {key[1]}")
            return False

        primary_dns, secondary_dns = dns_settings

        # Set IP address, subnet mask, and gateway
        ps_script_ip = f"""
        $adapter = Get-NetAdapter | Where-Object {{ $_.Status -eq "Up" }} | Select-Object -First 1
        $interfaceIndex = $adapter.InterfaceIndex

        # Remove existing IP configuration
        Remove-NetIPAddress -InterfaceIndex $interfaceIndex -Confirm:$false -ErrorAction SilentlyContinue
        Remove-NetRoute -InterfaceIndex $interfaceIndex -Confirm:$false -ErrorAction SilentlyContinue

        # Set new IP configuration
        New-NetIPAddress -InterfaceIndex $interfaceIndex -IPAddress '{ip_address}' -PrefixLength {subnet_mask} -DefaultGateway '{gateway}'
        """

        ip_command = session.run_ps(ps_script_ip)
        ip_status_code = ip_command.status_code

        if ip_status_code != 0:
            logging.error(f"Failed to set IP configuration. Status code: {ip_status_code}")
            if ip_command.std_err:
                logging.error(f"Error details: {ip_command.std_err.decode('utf-8')}")
            return False

        # Set DNS servers
        ps_script_dns = f"""
        $adapter = Get-NetAdapter | Where-Object {{ $_.Status -eq "Up" }} | Select-Object -First 1
        Set-DnsClientServerAddress -InterfaceAlias $adapter.Name -ServerAddresses "{primary_dns}", "{secondary_dns}"
        """

        dns_command = session.run_ps(ps_script_dns)
        dns_status_code = dns_command.status_code

        if ip_status_code == 0 and dns_status_code == 0:
            logging.info(f"IP Address set to: {ip_address}")
            logging.info(f"Subnet Mask set to: /{subnet_mask}")
            logging.info(f"Default Gateway set to: {gateway}")
            logging.info(f"DNS Servers set to: {primary_dns}, {secondary_dns}")
            logging.info(f"DNS updated successfully for Domain: {domain}, Location: {location}")
            return True
        else:
            logging.error(f"Failed to update IP configuration. IP Status: {ip_status_code}, DNS Status: {dns_status_code}")
            if dns_command.std_err:
                logging.error(f"DNS Error details: {dns_command.std_err.decode('utf-8')}")
            return False

    except Exception as e:
        logging.error("Automation failed to update IP configuration on the server. Please check this issue manually.")
        logging.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    # Check arguments
    if len(sys.argv) < 9:
        logging.error("Usage: python step2_update_ip_config.py HOST USERNAME PASSWORD IP_ADDRESS SUBNET_MASK GATEWAY DOMAIN LOCATION")
        logging.error("Example: python step2_update_ip_config.py ************* domain\\admin Password123 **********0 24 ********** ESI COLO")
        sys.exit(1)

    # Get arguments
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]
    ip_address = sys.argv[4]
    subnet_mask = sys.argv[5]
    gateway = sys.argv[6]
    domain = sys.argv[7]
    location = sys.argv[8]

    # Run the IP configuration update
    success = update_ip_config(host, username, password, ip_address, subnet_mask, gateway, domain, location)

    # Exit with appropriate code
    sys.exit(0 if success else 1)