#!/usr/bin/env python
"""
Step 2: Update DNS Settings

This script updates DNS settings based on domain and location using PowerShell commands executed via WinRM.

Usage:
  python step2_update_dns.py HOST USERNAME PASSWORD DOMAIN LOCATION

  Example:
  python step2_update_dns.py ************* domain\\admin Password123 ESI COLO
"""
import sys
import time
import logging
import winrm
import traceback

# DNS Mappings for different domains and locations
DNS_MAPPINGS = {
    ("ESI", "COLO"): ("************", "*************"),
    ("ESI", "Nutley"): ("************", "************"),
    ("ESI", "Baltimore"): ("************", "************"),
    ("ESI", "Raleigh"): ("*************", "*************"),
    ("EGC", "COLO"): ("************", "*************"),
    ("<PERSON><PERSON>", "Nut<PERSON>"): ("************", "************"),
    ("ESI", "AWS"): ("************", "************"),
    ("EGC", "AWS"): ("***********", "************")
}

def check_server_uptime(session):
    """
    Checks the uptime of the server using PowerShell.
    Returns True if the server is up, otherwise False.
    """
    ps_script = "(Get-CimInstance -ClassName Win32_OperatingSystem).LastBootUpTime"
    try:
        response = session.run_ps(ps_script)
        if response.status_code == 0:
            uptime = response.std_out.decode('utf-8').strip()
            logging.info(f"Server is up. Uptime: {uptime}")
            return True
        else:
            logging.error(f"Error checking server uptime: {response.std_err.decode('utf-8')}")
            return False
    except Exception as e:
        logging.error(f"Exception during uptime check: {e}")
        return False

def update_dns(host, username, password, domain, location):
    """Update DNS settings based on domain and location"""
    try:
        logging.info(f"Step 2: Updating DNS Settings for Domain: {domain}, Location: {location}")

        # Wait for 5 minutes before attempting connection
        logging.info("Waiting for 5 minutes before attempting connection to the server...")
        time.sleep(300)

        # Create WinRM session
        session = winrm.Session(host, auth=(username, password), transport='ntlm')

        # Check server uptime before proceeding
        if not check_server_uptime(session):
            logging.error("Server not responding")
            return False

        # Get DNS settings based on domain and location
        dns_settings = DNS_MAPPINGS.get((domain, location))
        if not dns_settings:
            logging.error(f"Invalid domain ({domain}) or location ({location}) provided.")
            logging.info("Valid combinations are:")
            for key in DNS_MAPPINGS.keys():
                logging.info(f"  - Domain: {key[0]}, Location: {key[1]}")
            return False

        primary_dns, secondary_dns = dns_settings

        # PowerShell script for DNS configuration
        ps_script = f"""
        $adapter = Get-NetAdapter | Where-Object {{ $_.Status -eq "Up" }}
        Set-DnsClientServerAddress -InterfaceAlias $adapter.Name -ServerAddresses "{primary_dns}", "{secondary_dns}"
        """

        logging.info(f"Executing DNS update for Domain: {domain}, Location: {location}")
        file_command = session.run_ps(ps_script)
        status_code = file_command.status_code

        if status_code == 0:
            logging.info(f"DNS updated successfully for Domain: {domain}, Location: {location}")
            logging.info(f"Primary DNS: {primary_dns}")
            logging.info(f"Secondary DNS: {secondary_dns}")
            return True
        else:
            logging.error(f"Error while updating DNS for Domain: {domain}, Location: {location}.")
            logging.error(file_command.std_err.decode('utf-8') if file_command.std_err else "No error details available.")
            return False

    except Exception as e:
        logging.error("Automation failed to update DNS on the impacted server. Please check this issue manually.")
        logging.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    # Check arguments
    if len(sys.argv) < 6:
        logging.error("Usage: python step2_update_ip_config.py HOST USERNAME PASSWORD DOMAIN LOCATION")
        logging.error("Example: python step2_update_ip_config.py ************* domain\\admin Password123 ESI COLO")
        sys.exit(1)

    # Get arguments
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]
    domain = sys.argv[4]
    location = sys.argv[5]

    # Run the DNS update operation
    success = update_dns(host, username, password, domain, location)

    # Exit with appropriate code
    sys.exit(0 if success else 1)