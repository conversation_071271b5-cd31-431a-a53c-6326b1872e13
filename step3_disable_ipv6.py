#!/usr/bin/env python
"""
Step 3: Disable IPv6

This script disables IPv6 on all network adapters of a Windows server using PowerShell commands executed via WinRM.

Usage:
  python step3_disable_ipv6.py HOST USERNAME PASSWORD
  
  Example:
  python step3_disable_ipv6.py ************* domain\\admin Password123
"""
import sys
import logging
from config import client_login, run_command

def disable_ipv6(host, username, password):
    """Disable IPv6 on all network adapters"""
    logging.info("Step 3: Disabling IPv6")

    # Login to the server
    session, original_policy = client_login(host, username, password)
    if not session:
        logging.error(f"Failed to connect to {host}. Check credentials and WinRM configuration.")
        return False

    # Disable IPv6 on all network adapters
    cmd = """
    $adapters = Get-NetAdapter
    foreach ($adapter in $adapters) {
        Disable-NetAdapterBinding -InterfaceAlias $adapter.Name -ComponentID 'ms_tcpip6'
    }
    """
    status, output = run_command(session, cmd, "Disable IPv6", host)

    if status == "Success":
        logging.info("IPv6 has been disabled on all network adapters")
        return True
    else:
        logging.error(f"Failed to disable IPv6: {output}")
        return False

if __name__ == "__main__":
    # Check arguments
    if len(sys.argv) < 4:
        logging.error("Usage: python step3_disable_ipv6.py HOST USERNAME PASSWORD")
        logging.error("Example: python step3_disable_ipv6.py ************* domain\\admin Password123")
        sys.exit(1)

    # Get arguments
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]

    # Run the IPv6 disable operation
    success = disable_ipv6(host, username, password)

    # Exit with appropriate code
    sys.exit(0 if success else 1)