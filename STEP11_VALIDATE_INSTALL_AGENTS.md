# Step 11: Validate and Install Required Agents

## Overview
Step 11 validates if required security and monitoring agents are installed and running on the server. It checks different agent sets based on the environment (On-prem vs AWS Cloud).

## Required Agents by Environment

### **On-prem Environment:**
1. **Splunk UF agent** - Log forwarding and monitoring
2. **Carbon Black** - Endpoint detection and response
3. **Symantec Endpoint AV** - Antivirus protection

### **AWS Cloud Environment:**
1. **Splunk UF agent** - Log forwarding and monitoring
2. **Carbon Black** - Endpoint detection and response
3. **Trend Micro AV** - Cloud antivirus protection
4. **Qualys scanner** - Vulnerability scanning

## Features

### **1. Environment-Aware Validation**
- **Automatic agent set selection** based on environment parameter
- **Different requirements** for on-prem vs AWS
- **Comprehensive agent definitions** with service and process details

### **2. Multi-Level Agent Checking**
- **Service status validation** - Checks if service exists and is running
- **Process verification** - Validates agent processes are active
- **Automatic service restart** - Attempts to start stopped services

### **3. Detailed Reporting**
- **Visual status indicators** (✓, ⚠, ✗)
- **Comprehensive logging** of all validation steps
- **Summary reports** with actionable information

### **4. Manual Installation Guidance**
- **Lists missing agents** that require manual installation
- **Provides clear next steps** for remediation
- **Distinguishes between missing and stopped agents**

## Usage

### **Command Format:**
```bash
python step11_validate_install_agents.py HOST USERNAME PASSWORD ENVIRONMENT

# Examples:
python step11_validate_install_agents.py ************* domain\admin Password123 onprem
python step11_validate_install_agents.py ************* domain\admin Password123 aws
```

### **Arguments:**
1. **HOST** - Target server IP/hostname
2. **USERNAME** - WinRM username
3. **PASSWORD** - WinRM password
4. **ENVIRONMENT** - Environment type (onprem or aws)

## Agent Definitions

### **On-prem Agents:**
```python
"onprem": {
    "Splunk UF": {
        "service_name": "SplunkForwarder",
        "process_name": "splunkd.exe",
        "display_name": "Splunk Universal Forwarder"
    },
    "Carbon Black": {
        "service_name": "CarbonBlack",
        "process_name": "cb.exe",
        "display_name": "Carbon Black Response Sensor"
    },
    "Symantec Endpoint AV": {
        "service_name": "SepMasterService",
        "process_name": "ccSvcHst.exe",
        "display_name": "Symantec Endpoint Protection"
    }
}
```

### **AWS Cloud Agents:**
```python
"aws": {
    "Splunk UF": {
        "service_name": "SplunkForwarder",
        "process_name": "splunkd.exe",
        "display_name": "Splunk Universal Forwarder"
    },
    "Carbon Black": {
        "service_name": "CarbonBlack",
        "process_name": "cb.exe",
        "display_name": "Carbon Black Response Sensor"
    },
    "Trend Micro AV": {
        "service_name": "TMBMServer",
        "process_name": "TmListen.exe",
        "display_name": "Trend Micro Deep Security Agent"
    },
    "Qualys Scanner": {
        "service_name": "QualysAgent",
        "process_name": "QualysAgent.exe",
        "display_name": "Qualys Cloud Agent"
    }
}
```

## Validation Logic

### **PowerShell Service Check:**
```powershell
try {
    $service = Get-Service -Name 'SERVICE_NAME' -ErrorAction SilentlyContinue
    if ($service) {
        Write-Output "Service: SERVICE_NAME - Status: $($service.Status)"
        if ($service.Status -eq 'Running') {
            Write-Output "INSTALLED_RUNNING"
        } else {
            Write-Output "INSTALLED_STOPPED"
        }
    } else {
        Write-Output "SERVICE_NOT_FOUND"
    }
}
catch {
    Write-Output "SERVICE_CHECK_ERROR: $($_.Exception.Message)"
}
```

### **Service Start Attempt:**
```powershell
try {
    Start-Service -Name 'SERVICE_NAME' -ErrorAction Stop
    Write-Output "SERVICE_STARTED"
}
catch {
    Write-Output "START_FAILED: $($_.Exception.Message)"
}
```

## Expected Output

### **All Agents Running (Success):**
```
INFO - Step 11: Validating and Installing Required Agents for ONPREM environment
INFO - Required agents for ONPREM environment:
INFO -   - Splunk UF
INFO -   - Carbon Black
INFO -   - Symantec Endpoint AV
INFO - Connected to server: *************
INFO - Checking Splunk UF...
INFO - ✓ Splunk UF is installed and running
INFO - Checking Carbon Black...
INFO - ✓ Carbon Black is installed and running
INFO - Checking Symantec Endpoint AV...
INFO - ✓ Symantec Endpoint AV is installed and running
INFO - ============================================================
INFO - AGENT VALIDATION RESULTS:
INFO - ============================================================
INFO - ✓ Splunk UF: INSTALLED AND RUNNING
INFO - ✓ Carbon Black: INSTALLED AND RUNNING
INFO - ✓ Symantec Endpoint AV: INSTALLED AND RUNNING
INFO - ============================================================
INFO - ✓ ALL REQUIRED AGENTS ARE INSTALLED AND RUNNING
INFO - ============================================================
```

### **Some Agents Missing/Stopped:**
```
INFO - Step 11: Validating and Installing Required Agents for AWS environment
INFO - Required agents for AWS environment:
INFO -   - Splunk UF
INFO -   - Carbon Black
INFO -   - Trend Micro AV
INFO -   - Qualys Scanner
INFO - Connected to server: *************
INFO - Checking Splunk UF...
INFO - ✓ Splunk UF is installed and running
INFO - Checking Carbon Black...
WARNING - ⚠ Carbon Black is installed but not running
INFO - Attempting to start Carbon Black service...
INFO - ✓ Carbon Black service started successfully
INFO - Checking Trend Micro AV...
WARNING - ✗ Trend Micro AV is not installed
INFO - Checking Qualys Scanner...
WARNING - ✗ Qualys Scanner is not installed
INFO - ============================================================
INFO - AGENT VALIDATION RESULTS:
INFO - ============================================================
INFO - ✓ Splunk UF: INSTALLED AND RUNNING
INFO - ✓ Carbon Black: INSTALLED AND RUNNING
ERROR - ✗ Trend Micro AV: NOT INSTALLED
ERROR - ✗ Qualys Scanner: NOT INSTALLED
ERROR - ============================================================
ERROR - MISSING AGENTS REQUIRE MANUAL INSTALLATION:
ERROR - ============================================================
ERROR -   - Trend Micro AV
ERROR -   - Qualys Scanner
ERROR - Please install missing agents manually and re-run validation.
WARNING - ============================================================
WARNING - ⚠ SOME AGENTS REQUIRE ATTENTION
WARNING - ============================================================
```

## Return Values

### **Function Returns:**
- **`True`** - All required agents are installed and running
- **`False`** - Some agents are missing, stopped, or in unknown state

### **Script Exit Codes:**
- **`0`** - Success (all agents validated)
- **`1`** - Failure (some agents need attention)

## Usage Examples

### **On-prem Environment Validation:**
```bash
python step11_validate_install_agents.py ************* domain\admin Password123 onprem
```
**Checks:** Splunk UF, Carbon Black, Symantec Endpoint AV

### **AWS Cloud Environment Validation:**
```bash
python step11_validate_install_agents.py ************* domain\admin Password123 aws
```
**Checks:** Splunk UF, Carbon Black, Trend Micro AV, Qualys Scanner

## Integration with Server Setup Process

### **Updated Server Setup Sequence:**
1. **Step 1**: Rename Server
2. **Step 2**: Update DNS Settings
3. **Step 3**: Disable IPv6
4. **Step 4**: Disable Print Spooler
5. **Step 5**: Join Domain
6. **Step 6**: Move Computer to WCL/Server OU
7. **Step 7**: Placeholder (redirects to Step 2)
8. **Step 8**: Check Windows Activation Status
9. **Step 9**: Check Windows Updates
10. **Step 10**: Add Computer to Security Group
11. **Step 11**: Validate and Install Required Agents ← **NEW STEP**

### **Logical Placement:**
Step 11 is placed at the end because:
- **Server configuration is complete** (domain, OU, groups)
- **Network connectivity is established** (DNS, domain join)
- **Final validation step** before server handover
- **Security agents are typically installed last**

## Integration with Rundeck

### **Rundeck Job Options:**
```
TARGET_HOST = ************* (Target server)
USERNAME = domain\admin (WinRM username)
PASSWORD = [secure] (WinRM password)
ENVIRONMENT = onprem/aws (Environment type)
```

### **Rundeck Step Configuration:**
```bash
python step11_validate_install_agents.py ${option.TARGET_HOST} ${option.USERNAME} ${option.PASSWORD} ${option.ENVIRONMENT}
```

### **Rundeck Workflow Logic:**
- **Success**: All agents validated, server ready for production
- **Failure**: Manual intervention required for missing agents

## Manual Installation Requirements

### **When Agents are Missing:**
1. **Download agent installers** from respective vendors
2. **Install agents manually** on the target server
3. **Configure agents** according to organizational policies
4. **Re-run Step 11** to validate installation
5. **Proceed with server handover** once all agents are validated

### **Common Installation Paths:**
- **Splunk UF**: Usually installed via MSI package
- **Carbon Black**: Deployed via enterprise console
- **Symantec/Trend Micro**: Installed via management console
- **Qualys**: Agent deployed via Qualys platform

Step 11 provides comprehensive agent validation to ensure servers meet security and monitoring requirements before production deployment!
