# Simple Script Usage Guide

All scripts now use simple `sys.argv` approach - no classes, no complex functions.

## Usage Pattern

All scripts follow this simple pattern:
```python
var1 = sys.argv[1]
var2 = sys.argv[2]
var3 = sys.argv[3]
# etc...
```

## Individual Script Usage

### Step 1: Rename Server
```bash
python step1_rename_server.py HOST USERNAME PASSWORD NEW_HOSTNAME

# Example:
python step1_rename_server.py ************* domain\admin Password123 NEWSERVER01
```

### Step 2: Update IP Configuration
```bash
python step2_update_ip_config.py HOST USERNAME PASSWORD IP_ADDRESS SUBNET_MASK GATEWAY DNS_SERVERS

# Example:
python step2_update_ip_config.py ************* domain\admin Password123 **********0 24 ********** "************,************"
```

### Step 3: Disable IPv6
```bash
python step3_disable_ipv6.py HOST USERNAME PASSWORD

# Example:
python step3_disable_ipv6.py ************* domain\admin Password123
```

### Step 4: Disable Print Spooler
```bash
python step4_disable_print_spooler.py HOST USERNAME PASSWORD

# Example:
python step4_disable_print_spooler.py ************* domain\admin Password123
```

### Step 5: Join Domain
```bash
python step5_join_domain.py HOST USERNAME PASSWORD DOMAIN DOMAIN_USERNAME DOMAIN_PASSWORD

# Example:
python step5_join_domain.py ************* domain\admin Password123 ESI.US.EISAI.LOCAL domain\admin DomainPass123
```

### Step 6: Move Computer to WCL/Server OU
```bash
python step6_move_computer_ou.py HOST USERNAME PASSWORD NEW_HOSTNAME DOMAIN

# Example:
python step6_move_computer_ou.py ************* domain\admin Password123 NEWSERVER01 ESI.US.EISAI.LOCAL
```

### Step 7: Update DNS Settings
```bash
python step7_update_dns.py HOST USERNAME PASSWORD DOMAIN_SHORT LOCATION

# Example:
python step7_update_dns.py ************* domain\admin Password123 ESI COLO
```

### Step 8: Activate Windows
```bash
python step8_activate_windows.py HOST USERNAME PASSWORD

# Example:
python step8_activate_windows.py ************* domain\admin Password123
```

### Step 9: Check Windows Updates
```bash
python step9_check_windows_updates.py HOST USERNAME PASSWORD

# Example:
python step9_check_windows_updates.py ************* domain\admin Password123
```

## Argument Positions

### Common Arguments (All Scripts):
- `sys.argv[1]` = HOST (Target server IP/hostname)
- `sys.argv[2]` = USERNAME (WinRM username)
- `sys.argv[3]` = PASSWORD (WinRM password)

### Step-Specific Arguments:
- **Step 1**: `sys.argv[4]` = NEW_HOSTNAME
- **Step 2**: `sys.argv[4]` = IP_ADDRESS, `sys.argv[5]` = SUBNET_MASK, `sys.argv[6]` = GATEWAY, `sys.argv[7]` = DNS_SERVERS
- **Step 3**: No additional arguments
- **Step 4**: No additional arguments
- **Step 5**: `sys.argv[4]` = DOMAIN, `sys.argv[5]` = DOMAIN_USERNAME, `sys.argv[6]` = DOMAIN_PASSWORD
- **Step 6**: `sys.argv[4]` = NEW_HOSTNAME, `sys.argv[5]` = DOMAIN
- **Step 7**: `sys.argv[4]` = DOMAIN_SHORT, `sys.argv[5]` = LOCATION
- **Step 8**: No additional arguments
- **Step 9**: No additional arguments

## Error Handling

Each script checks for the correct number of arguments:
```python
if len(sys.argv) < REQUIRED_COUNT:
    logging.error("Usage: python scriptname.py [ARGS...]")
    logging.error("Example: python scriptname.py arg1 arg2 arg3")
    sys.exit(1)
```

## Features

✅ **Simple**: No classes, no complex functions
✅ **Direct**: Use `sys.argv[1]`, `sys.argv[2]`, etc. directly
✅ **Consistent**: All scripts follow the same pattern
✅ **Python 3.6 Compatible**: No complex syntax
✅ **Logging**: All scripts use proper logging instead of print
✅ **Error Handling**: Clear error messages for missing arguments

## Full Server Setup Example

```bash
# Step 1: Rename
python step1_rename_server.py ************* domain\admin Pass123 NEWSERVER01

# Step 2: IP Config
python step2_update_ip_config.py ************* domain\admin Pass123 **********0 24 ********** "************,************"

# Step 3: Disable IPv6
python step3_disable_ipv6.py ************* domain\admin Pass123

# Step 4: Disable Print Spooler
python step4_disable_print_spooler.py ************* domain\admin Pass123

# Step 5: Join Domain
python step5_join_domain.py ************* domain\admin Pass123 ESI.US.EISAI.LOCAL domain\admin DomainPass123

# Step 6: Move Computer OU (Manual)
python step6_move_computer_ou.py ************* domain\admin Pass123 NEWSERVER01 ESI.US.EISAI.LOCAL

# Step 7: Update DNS
python step7_update_dns.py ************* domain\admin Pass123 ESI COLO

# Step 8: Activate Windows
python step8_activate_windows.py ************* domain\admin Pass123

# Step 9: Check Updates
python step9_check_windows_updates.py ************* domain\admin Pass123
```

This approach is much simpler and should work perfectly with Python 3.6!
