#!/usr/bin/env python
"""
Step 10: Add Computer Object to Security Group

This script adds the computer object to the appropriate security group based on domain:
- ESI Domain: UG_ESI_Mamoru_Wincollector
- EGC Domain: UG_EGC_Mamoru_Wincollector

Usage:
  python step10_add_computer_to_group.py HOST USERNAME PASSWORD COMPUTER_NAME DOMAIN LOCATION
  
  Example:
  python step10_add_computer_to_group.py ************* domain\\admin Password123 NEWSERVER01 ESI COLO
"""
import sys
import time
import logging
import winrm
import traceback

def add_computer_to_group(username, password, computer_name, domain, location):
    """Add computer object to appropriate security group based on domain"""
    try:
        logging.info(f"Step 10: Adding computer {computer_name} to security group")
        
        # Add a 5-minute delay before connecting to the domain controller
        logging.info("Waiting for 5 minutes before attempting connection to the domain controller...")
        time.sleep(300)
        
        # Determine domain controller based on domain and location
        if domain == 'ESI' and location == 'COLO':
            domain_controller = "esidcprod03"
        elif domain == 'ESI' and location == 'Raleigh':
            domain_controller = 'RALDC01'
        elif domain == 'ESI' and location == 'Baltimore':
            domain_controller = 'ESIBALDCWP01'
        elif domain == 'ESI' and location == 'Nutley':
            domain_controller = 'ESINUTDCWP01'
        elif domain == 'ESI' and location == 'AWS':
            domain_controller = 'ESIAWSNVDC02'
        elif domain == 'EGC' and location == 'COLO':
            domain_controller = 'EGCDC03'
        elif domain == 'EGC' and location == 'Nutley':
            domain_controller = 'EGCDC05'
        else:
            domain_controller = 'EGCDC07'
            
        # Connect to the Domain Controller
        session = winrm.Session(domain_controller, auth=(username, password), transport="ntlm")
        logging.info(f"Successfully connected to the Domain Controller: {domain_controller}")
        
        # Determine the security group based on domain
        if domain == "ESI":
            security_group = "UG_ESI_Mamoru_Wincollector"
        elif domain == "EGC":
            security_group = "UG_EGC_Mamoru_Wincollector"
        else:
            logging.error("Invalid domain specified. Must be ESI or EGC.")
            return False

        # Construct the PowerShell command to add computer to group
        command = f"""
        try {{
            # Get the computer object
            $Computer = Get-ADComputer -Identity '{computer_name}' -ErrorAction Stop
            
            # Get the security group
            $Group = Get-ADGroup -Identity '{security_group}' -ErrorAction Stop
            
            # Check if computer is already a member
            $IsMember = Get-ADGroupMember -Identity '{security_group}' | Where-Object {{ $_.Name -eq '{computer_name}' }}
            
            if ($IsMember) {{
                Write-Output "Computer {computer_name} is already a member of {security_group}"
            }} else {{
                # Add computer to the security group
                Add-ADGroupMember -Identity '{security_group}' -Members $Computer -ErrorAction Stop
                Write-Output "Successfully added {computer_name} to {security_group}"
            }}
        }}
        catch {{
            Write-Error "Failed to add computer to group: $($_.Exception.Message)"
            throw
        }}
        """

        logging.info(f"Adding {computer_name} to security group: {security_group}")
        logging.info(f"Executing command on Domain Controller: {domain_controller}")
        
        # Run the PowerShell script
        file_command = session.run_ps(command)

        # Get the status code
        status_code = file_command.status_code
        logging.info(f"Command status code: {status_code}")

        if status_code == 0:
            output = file_command.std_out.decode('utf-8').strip()
            logging.info(f"Operation completed successfully")
            logging.info(f"Output: {output}")
            logging.info(f"Computer {computer_name} processed for group {security_group}")
            return True
        else:
            logging.error(f"Failed to add computer to security group. Status code: {status_code}")
            if file_command.std_err:
                error_details = file_command.std_err.decode('utf-8')
                logging.error(f"Error details: {error_details}")
            return False

    except Exception as e:
        logging.error(f"Error adding computer to security group: {e}")
        logging.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    # Check arguments
    if len(sys.argv) < 7:
        logging.error("Usage: python step10_add_computer_to_group.py HOST USERNAME PASSWORD COMPUTER_NAME DOMAIN LOCATION")
        logging.error("Example: python step10_add_computer_to_group.py ************* domain\\admin Password123 NEWSERVER01 ESI COLO")
        sys.exit(1)
    
    # Get arguments
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]
    computer_name = sys.argv[4]
    domain = sys.argv[5]
    location = sys.argv[6]
    
    # Run the add computer to group operation
    success = add_computer_to_group(username, password, computer_name, domain, location)
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
