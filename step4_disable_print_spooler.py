import sys
import logging
from config import client_login, run_command

def disable_print_spooler(host, username, password):
    """Disable Print Spooler service"""
    logging.info("Step 4: Disabling Print Spooler Service")

    # Login to the server
    session, original_policy = client_login(host, username, password)
    if not session:
        logging.error(f"Failed to connect to {host}. Check credentials and WinRM configuration.")
        return False

    # Stop and disable the Print Spooler service
    cmd = """
    Stop-Service -Name Spooler -Force
    Set-Service -Name Spooler -StartupType Disabled
    """
    status, output = run_command(session, cmd, "Disable Print Spooler", host)

    if status == "Success":
        logging.info("Print Spooler service has been stopped and disabled")
        return True
    else:
        logging.error(f"Failed to disable Print Spooler service: {output}")
        return False

if __name__ == "__main__":
    # Check arguments
    if len(sys.argv) < 4:
        logging.error("Usage: python step4_disable_print_spooler.py HOST USERNAME PASSWORD")
        logging.error("Example: python step4_disable_print_spooler.py ************* domain\\admin Password123")
        sys.exit(1)

    # Get arguments
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]

    # Run the Print Spooler disable operation
    success = disable_print_spooler(host, username, password)

    # Exit with appropriate code
    sys.exit(0 if success else 1)