#!/usr/bin/env python
"""
Test script to verify syntax compatibility with Python 3.6
"""

def test_imports():
    """Test that all imports work correctly"""
    try:
        print("Testing global_args import...")
        from global_args import get_step1_args, get_step2_args, get_step3_args
        print("✓ global_args import successful")
        
        print("Testing step script imports...")
        import step1_rename_server
        import step2_update_ip_config
        import step3_disable_ipv6
        print("✓ Step script imports successful")
        
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_syntax():
    """Test basic syntax elements"""
    try:
        # Test f-string compatibility
        test_var = "test"
        result = f"This is a {test_var}"
        print(f"✓ F-string test: {result}")
        
        # Test string formatting
        dns_list = ["*******", "*******"]
        dns_addresses = ','.join([f"'{dns.strip()}'" for dns in dns_list])
        print(f"✓ DNS formatting test: {dns_addresses}")
        
        return True
    except Exception as e:
        print(f"✗ Syntax test failed: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 50)
    print("Python 3.6 Compatibility Test")
    print("=" * 50)
    
    import sys
    print(f"Python version: {sys.version}")
    print()
    
    # Test imports
    import_success = test_imports()
    print()
    
    # Test syntax
    syntax_success = test_syntax()
    print()
    
    if import_success and syntax_success:
        print("✓ All tests passed! Scripts should work on Python 3.6")
        return 0
    else:
        print("✗ Some tests failed. Check the errors above.")
        return 1

if __name__ == "__main__":
    exit(main())
