#!/usr/bin/env python
"""
Step 4: Disable Print Spooler Service

This script disables the Print Spooler service on a Windows server using PowerShell commands executed via WinRM.

Usage:
  python step4_disable_print_spooler.py HOST USERNAME PASSWORD
  
  Example:
  python step4_disable_print_spooler.py ************* domain\\admin Password123
"""
import sys
import logging
from config import client_login, run_command
from global_args import get_step4_args

def disable_print_spooler(host, username, password):
    """Disable Print Spooler service"""
    logging.info("Step 4: Disabling Print Spooler Service")

    # Login to the server
    session, original_policy = client_login(host, username, password)
    if not session:
        logging.error(f"Failed to connect to {host}. Check credentials and WinRM configuration.")
        return False

    # Stop and disable the Print Spooler service
    cmd = """
    Stop-Service -Name Spooler -Force
    Set-Service -Name Spooler -StartupType Disabled
    """
    status, output = run_command(session, cmd, "Disable Print Spooler", host)

    if status == "Success":
        logging.info("Print Spooler service has been stopped and disabled")
        return True
    else:
        logging.error(f"Failed to disable Print Spooler service: {output}")
        return False

if __name__ == "__main__":
    # Get standardized arguments
    args = get_step4_args()

    # Run the Print Spooler disable operation
    success = disable_print_spooler(args['host'], args['username'], args['password'])

    # Exit with appropriate code
    sys.exit(0 if success else 1)