#!/usr/bin/env python
"""
Simple Global Arguments Module

Simple functions to get command line arguments using sys.argv directly.
No classes, just simple functions.

Usage in scripts:
  host = sys.argv[1]
  username = sys.argv[2]
  password = sys.argv[3]
"""
import sys
import logging

def check_args(min_args, script_name):
    """Check if minimum arguments are provided"""
    if len(sys.argv) < min_args:
        logging.error(f"Usage: python {script_name} [ARGS...]")
        logging.error(f"Provided {len(sys.argv)-1} arguments, need at least {min_args-1}")
        sys.exit(1)

# Simple functions for each step
def get_step1_args():
    """Get arguments for step1: HOST USERNAME PASSWORD NEW_HOSTNAME"""
    check_args(5, "step1_rename_server.py")
    return {
        'host': sys.argv[1],
        'username': sys.argv[2],
        'password': sys.argv[3],
        'new_hostname': sys.argv[4]
    }

def get_step2_args():
    """Get arguments for step2: HOST USERNAME PASSWORD IP_ADDRESS SUBNET_MASK GATEWAY DNS_SERVERS"""
    check_args(8, "step2_update_ip_config.py")
    return {
        'host': sys.argv[1],
        'username': sys.argv[2],
        'password': sys.argv[3],
        'ip_address': sys.argv[4],
        'subnet_mask': sys.argv[5],
        'gateway': sys.argv[6],
        'dns_servers': sys.argv[7]
    }

def get_step3_args():
    """Get arguments for step3: HOST USERNAME PASSWORD"""
    check_args(4, "step3_disable_ipv6.py")
    return {
        'host': sys.argv[1],
        'username': sys.argv[2],
        'password': sys.argv[3]
    }

def get_step4_args():
    """Get arguments for step4: HOST USERNAME PASSWORD"""
    check_args(4, "step4_disable_print_spooler.py")
    return {
        'host': sys.argv[1],
        'username': sys.argv[2],
        'password': sys.argv[3]
    }

def get_step5_args():
    """Get arguments for step5: HOST USERNAME PASSWORD DOMAIN DOMAIN_USERNAME DOMAIN_PASSWORD"""
    check_args(7, "step5_join_domain.py")
    return {
        'host': sys.argv[1],
        'username': sys.argv[2],
        'password': sys.argv[3],
        'domain': sys.argv[4],
        'domain_username': sys.argv[5],
        'domain_password': sys.argv[6]
    }

def get_step6_args():
    """Get arguments for step6: HOST USERNAME PASSWORD NEW_HOSTNAME DOMAIN"""
    check_args(6, "step6_move_computer_ou.py")
    return {
        'host': sys.argv[1],
        'username': sys.argv[2],
        'password': sys.argv[3],
        'new_hostname': sys.argv[4],
        'domain': sys.argv[5]
    }

def get_step7_args():
    """Get arguments for step7: HOST USERNAME PASSWORD DOMAIN_SHORT LOCATION"""
    check_args(6, "step7_update_dns.py")
    return {
        'host': sys.argv[1],
        'username': sys.argv[2],
        'password': sys.argv[3],
        'domain_short': sys.argv[4],
        'location': sys.argv[5]
    }

def get_step8_args():
    """Get arguments for step8: HOST USERNAME PASSWORD"""
    check_args(4, "step8_activate_windows.py")
    return {
        'host': sys.argv[1],
        'username': sys.argv[2],
        'password': sys.argv[3]
    }

def get_step9_args():
    """Get arguments for step9: HOST USERNAME PASSWORD"""
    check_args(4, "step9_check_windows_updates.py")
    return {
        'host': sys.argv[1],
        'username': sys.argv[2],
        'password': sys.argv[3]
    }
