#!/usr/bin/env python
"""
Step 8: Check Windows Activation Status

This script checks the Windows activation status on a server using PowerShell commands executed via WinRM.

Usage:
  python step8_activate_windows.py HOST USERNAME PASSWORD

  Example:
  python step8_activate_windows.py ************* domain\\admin Password123
"""
import sys
import logging
from config import client_login, run_command

def check_windows_activation(host, username, password):
    """Check Windows activation status"""
    logging.info("Step 8: Checking Windows Activation Status")

    # Login to the server
    session, original_policy = client_login(host, username, password)
    if not session:
        logging.error(f"Failed to connect to {host}. Check credentials and WinRM configuration.")
        return False

    # Check activation status
    cmd = "cscript C:\\Windows\\System32\\slmgr.vbs /dli"
    status, output = run_command(session, cmd, "Check Activation Status", host)

    if status == "Success":
        if "License Status: Licensed" in output:
            logging.info("Windows is activated - License Status: Licensed")
            return True
        else:
            logging.warning("Windows is not activated or activation status unclear")
            logging.info(f"Activation details: {output}")
            return False
    else:
        logging.error(f"Failed to check Windows activation status: {output}")
        return False

if __name__ == "__main__":
    # Check arguments
    if len(sys.argv) < 4:
        logging.error("Usage: python step8_activate_windows.py HOST USERNAME PASSWORD")
        logging.error("Example: python step8_activate_windows.py ************* domain\\admin Password123")
        logging.error("Note: This script only checks activation status, it does not activate Windows")
        sys.exit(1)

    # Get arguments
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]

    # Check the Windows activation status
    success = check_windows_activation(host, username, password)

    # Exit with appropriate code
    sys.exit(0 if success else 1)
