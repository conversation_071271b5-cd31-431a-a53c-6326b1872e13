#!/usr/bin/env python
"""
Step 6: Move Computer Object to WCL/Server OU

This script provides instructions for moving the computer object to WCL/Server OU.
This step requires manual intervention on the domain controller.

Usage:
  python step6_move_computer_ou.py HOST USERNAME PASSWORD NEW_HOSTNAME DOMAIN
  
  Example:
  python step6_move_computer_ou.py ************* domain\\admin Password123 NEWSERVER01 ESI.US.EISAI.LOCAL
"""
import sys
import logging
from config import client_login, run_command
from global_args import get_step6_args

def move_computer_ou(host, username, password, new_hostname, domain, domain_controller="esidcprod03"):
    """Move Computer object to WCL/Server OU"""
    logging.info(f"Step 6: Moving Computer object to WCL/Server OU")

    # Login to the server to verify connection
    session, original_policy = client_login(host, username, password)
    if not session:
        logging.error(f"Failed to connect to {host}. Check credentials and WinRM configuration.")
        return False

    logging.info(f"Computer Name: {new_hostname}")
    logging.info(f"Domain: {domain}")
    logging.info(f"Domain Controller: {domain_controller}")
    logging.info("MANUAL STEPS REQUIRED:")
    logging.info("=" * 50)
    logging.info(f"1. Login to {domain_controller} Domain Controller")
    logging.info("2. Open Active Directory Users and Computers (ADUC) console")
    logging.info(f"3. Navigate to the 'Computers' container/OU")
    logging.info(f"4. Find the computer object: {new_hostname}")
    logging.info(f"5. Right-click on {new_hostname} and select 'Move...'")
    logging.info("6. Navigate to and select: WCL/Server OU")
    logging.info("7. Click 'OK' to move the computer object")
    logging.info("=" * 50)
    logging.info("Alternative PowerShell method (if you have domain admin rights):")
    logging.info(f"Move-ADObject -Identity 'CN={new_hostname},CN=Computers,DC=...' -TargetPath 'OU=Server,OU=WCL,DC=...'")
    logging.info("Note: This step requires domain administrator privileges")
    logging.info("Please complete this step manually and then continue with the next steps.")
    
    return True

if __name__ == "__main__":
    # Get standardized arguments
    args = get_step6_args()

    # Run the move computer OU operation
    success = move_computer_ou(args['host'], args['username'], args['password'],
                              args['new_hostname'], args['domain'])

    # Exit with appropriate code
    sys.exit(0 if success else 1)
