# Rundeck Options and Script Usage Guide

## Rundeck UI Job Options

When creating a Rundeck job for your Windows Server Post-Build Automation, use these options:

### **Step Control Options (Enable/Disable)**
```
ENABLE_STEP1 = true/false (Enable Step 1: Rename Server)
ENABLE_STEP2 = true/false (Enable Step 2: Update IP Configuration)
ENABLE_STEP3 = true/false (Enable Step 3: Disable IPv6)
ENABLE_STEP4 = true/false (Enable Step 4: Disable Print Spooler)
ENABLE_STEP5 = true/false (Enable Step 5: Join Domain)
ENABLE_STEP6 = true/false (Enable Step 6: Move Computer to WCL/Server OU)
ENABLE_STEP7 = true/false (Enable Step 7: Update DNS Settings)
ENABLE_STEP8 = true/false (Enable Step 8: Activate Windows)
ENABLE_STEP9 = true/false (Enable Step 9: Check Windows Updates)
```

### **Connection Options**
```
SCRIPT_PATH = /path/to/your/automation/scripts (Path to automation scripts)
TARGET_HOST = ************* (Target Windows server IP/hostname)
USERNAME = domain\admin (WinRM username)
PASSWORD = [secure] (WinRM password - secure field)
```

### **Server Configuration Options**
```
NEW_HOSTNAME = NEWSERVER01 (New hostname for server)
IP_ADDRESS = *********** (New IP address)
SUBNET_MASK = 24 (Subnet mask in CIDR notation)
GATEWAY = ********** (Default gateway)
DNS_SERVERS = ************,************ (DNS servers comma-separated)
```

### **Domain Options**
```
DOMAIN = ESI.US.EISAI.LOCAL (Full domain name)
DOMAIN_SHORT = ESI (Short domain name for DNS - ESI/EGC)
DOMAIN_USERNAME = domain\admin (Domain admin username)
DOMAIN_PASSWORD = [secure] (Domain admin password - secure field)
```

### **Environment Options**
```
ENVIRONMENT = onprem (Server environment - onprem/aws)
LOCATION = COLO (Server location - COLO/Nutley/Baltimore/Raleigh/AWS)
NOTIFICATION_EMAIL = <EMAIL> (Email for notifications)
```

## Individual Script Usage

### **Step 1: Rename Server**
```bash
python step1_rename_server.py HOST USERNAME PASSWORD NEW_HOSTNAME

# Example:
python step1_rename_server.py ************* domain\admin Password123 NEWSERVER01
```

**Arguments:**
1. HOST - Target server IP/hostname
2. USERNAME - WinRM username (domain\username format)
3. PASSWORD - WinRM password
4. NEW_HOSTNAME - New hostname for the server

**What it does:**
- Connects to target server via WinRM
- Checks current hostname
- Renames server to specified hostname
- Requires restart after completion

---

### **Step 2: Update IP Configuration**
```bash
python step2_update_ip_config.py HOST USERNAME PASSWORD IP_ADDRESS SUBNET_MASK GATEWAY DNS_SERVERS

# Example:
python step2_update_ip_config.py ************* domain\admin Password123 *********** 24 ********** "************,************"
```

**Arguments:**
1. HOST - Target server IP/hostname
2. USERNAME - WinRM username
3. PASSWORD - WinRM password
4. IP_ADDRESS - New IP address
5. SUBNET_MASK - Subnet mask in CIDR notation (e.g., 24)
6. GATEWAY - Default gateway IP
7. DNS_SERVERS - DNS servers (comma-separated, in quotes)

**What it does:**
- Sets static IP address and subnet mask
- Configures default gateway
- Sets DNS servers
- Updates network adapter configuration

---

### **Step 3: Disable IPv6**
```bash
python step3_disable_ipv6.py HOST USERNAME PASSWORD

# Example:
python step3_disable_ipv6.py ************* domain\admin Password123
```

**Arguments:**
1. HOST - Target server IP/hostname
2. USERNAME - WinRM username
3. PASSWORD - WinRM password

**What it does:**
- Disables IPv6 on all network adapters
- Security hardening measure

---

### **Step 4: Disable Print Spooler**
```bash
python step4_disable_print_spooler.py HOST USERNAME PASSWORD

# Example:
python step4_disable_print_spooler.py ************* domain\admin Password123
```

**Arguments:**
1. HOST - Target server IP/hostname
2. USERNAME - WinRM username
3. PASSWORD - WinRM password

**What it does:**
- Stops Print Spooler service
- Disables Print Spooler service startup
- Security hardening measure

---

### **Step 5: Join Domain**
```bash
python step5_join_domain.py HOST USERNAME PASSWORD DOMAIN DOMAIN_USERNAME DOMAIN_PASSWORD

# Example:
python step5_join_domain.py ************* domain\admin Password123 ESI.US.EISAI.LOCAL domain\admin DomainPass123
```

**Arguments:**
1. HOST - Target server IP/hostname
2. USERNAME - WinRM username
3. PASSWORD - WinRM password
4. DOMAIN - Domain to join (e.g., ESI.US.EISAI.LOCAL)
5. DOMAIN_USERNAME - Domain admin username
6. DOMAIN_PASSWORD - Domain admin password

**What it does:**
- Checks current domain membership
- Joins server to specified domain
- Requires restart after completion

---

### **Step 6: Move Computer to WCL/Server OU**
```bash
python step6_move_computer_ou.py HOST USERNAME PASSWORD NEW_HOSTNAME DOMAIN

# Example:
python step6_move_computer_ou.py ************* domain\admin Password123 NEWSERVER01 ESI.US.EISAI.LOCAL
```

**Arguments:**
1. HOST - Target server IP/hostname
2. USERNAME - WinRM username
3. PASSWORD - WinRM password
4. NEW_HOSTNAME - Computer name in AD
5. DOMAIN - Domain name

**What it does:**
- Provides manual instructions for moving computer object
- Requires manual intervention on Domain Controller (esidcprod03)
- Move from Computers container to WCL/Server OU

---

### **Step 7: Update DNS Settings**
```bash
python step7_update_dns.py HOST USERNAME PASSWORD DOMAIN_SHORT LOCATION

# Example:
python step7_update_dns.py ************* domain\admin Password123 ESI COLO
```

**Arguments:**
1. HOST - Target server IP/hostname
2. USERNAME - WinRM username
3. PASSWORD - WinRM password
4. DOMAIN_SHORT - Domain short name (ESI or EGC)
5. LOCATION - Server location (COLO/Nutley/Baltimore/Raleigh/AWS)

**What it does:**
- Updates DNS servers based on domain and location
- Uses predefined DNS mappings from config.py
- Waits 5 minutes before execution

---

### **Step 8: Activate Windows**
```bash
python step8_activate_windows.py HOST USERNAME PASSWORD

# Example:
python step8_activate_windows.py ************* domain\admin Password123
```

**Arguments:**
1. HOST - Target server IP/hostname
2. USERNAME - WinRM username
3. PASSWORD - WinRM password

**What it does:**
- Runs Windows activation command
- Checks activation status
- Reports activation results

---

### **Step 9: Check Windows Updates**
```bash
python step9_check_windows_updates.py HOST USERNAME PASSWORD

# Example:
python step9_check_windows_updates.py ************* domain\admin Password123
```

**Arguments:**
1. HOST - Target server IP/hostname
2. USERNAME - WinRM username
3. PASSWORD - WinRM password

**What it does:**
- Checks for available Windows updates
- Reports number of available updates
- Provides guidance for manual update installation

---

## Validation Script Usage

### **Post-Build Validation**
```bash
# Run all validation checks
python post_server_build_validation_1.py

# Run specific validation checks
python post_server_build_validation_1.py 1 3 5

# Run single validation check
python post_server_build_validation_1.py 6
```

**Validation Checks:**
1. Hostname verification
2. IP Configuration check
3. IPv6 status verification
4. Print Spooler service status
5. Domain join verification
6. Computer OU placement check
7. DNS settings verification
8. Windows activation status
9. Required agents check
10. Windows updates status
11. Security group membership

## Common Usage Patterns

### **Full Server Setup (All Steps)**
```bash
# Step 1: Rename
python step1_rename_server.py ************* domain\admin Pass123 NEWSERVER01

# Step 2: IP Config
python step2_update_ip_config.py ************* domain\admin Pass123 *********** 24 ********** "************,************"

# Step 3: Disable IPv6
python step3_disable_ipv6.py ************* domain\admin Pass123

# Step 4: Disable Print Spooler
python step4_disable_print_spooler.py ************* domain\admin Pass123

# Step 5: Join Domain
python step5_join_domain.py ************* domain\admin Pass123 ESI.US.EISAI.LOCAL domain\admin DomainPass123

# Step 6: Move Computer OU (Manual)
python step6_move_computer_ou.py ************* domain\admin Pass123 NEWSERVER01 ESI.US.EISAI.LOCAL

# Step 7: Update DNS
python step7_update_dns.py ************* domain\admin Pass123 ESI COLO

# Step 8: Activate Windows
python step8_activate_windows.py ************* domain\admin Pass123

# Step 9: Check Updates
python step9_check_windows_updates.py ************* domain\admin Pass123

# Validation
python post_server_build_validation_1.py
```

### **Domain-Only Setup**
```bash
# Only domain-related steps
python step5_join_domain.py ************* domain\admin Pass123 ESI.US.EISAI.LOCAL domain\admin DomainPass123
python step6_move_computer_ou.py ************* domain\admin Pass123 NEWSERVER01 ESI.US.EISAI.LOCAL
python step7_update_dns.py ************* domain\admin Pass123 ESI COLO
```

### **Security-Only Setup**
```bash
# Only security hardening steps
python step3_disable_ipv6.py ************* domain\admin Pass123
python step4_disable_print_spooler.py ************* domain\admin Pass123
python step8_activate_windows.py ************* domain\admin Pass123
```

## Error Handling and Logging

All scripts now use proper logging:
- **Logs saved to**: `server_setup.log`
- **Console output**: Real-time progress
- **Log levels**: INFO, WARNING, ERROR
- **Timestamps**: All log entries include timestamps

## Prerequisites

1. **Python 3.x** installed on Rundeck server
2. **pywinrm module**: `pip install pywinrm`
3. **WinRM enabled** on target Windows servers
4. **Network connectivity** between Rundeck server and target servers
5. **Administrator credentials** for target servers
6. **Domain admin credentials** for domain operations
