#!/usr/bin/env python
"""
Step 6: Move Computer Object to WCL/Server OU

This script automatically moves the computer object from the default Computers container
to the WCL/Server OU in Active Directory using PowerShell commands.

Usage:
  python step6_move_computer_ou.py HOST USERNAME PASSWORD NEW_HOSTNAME DOMAIN LOCATION

Example:
  python step6_move_computer_ou.py ************* domain\\admin Password123 NEWSERVER01 ESI COLO

Arguments:
  HOST - Target server IP/hostname (not used but kept for consistency)
  USERNAME - Domain admin username
  PASSWORD - Domain admin password
  NEW_HOSTNAME - Computer name to move
  DOMAIN - Domain (ESI or EGC)
  LOCATION - Location (COLO, Raleigh, Baltimore, Nutley, AWS)
"""
import sys
import time
import logging
import winrm

def move_computer_object(username, password, domain, location, computer_name):
    """Move Computer object to WCL/Server OU using PowerShell commands"""
    try:
        # Add a 5-minute delay before connecting to the remote server
        logging.info("Waiting for 5 minutes before attempting connection to the server...")
        time.sleep(300)  # Wait for 5 minutes

        # Determine domain controller based on domain and location
        if domain == 'ESI' and location == 'COLO':
            domain_controller = "esidcprod03"
        elif domain == 'ESI' and location == 'Raleigh':
            domain_controller = 'RALDC01'
        elif domain == 'ESI' and location == 'Baltimore':
            domain_controller = 'ESIBALDCWP01'
        elif domain == 'ESI' and location == 'Nutley':
            domain_controller = 'ESINUTDCWP01'
        elif domain == 'ESI' and location == 'AWS':
            domain_controller = 'ESIAWSNVDC02'
        elif domain == 'EGC' and location == 'COLO':
            domain_controller = 'EGCDC03'
        elif domain == 'EGC' and location == 'Nutley':
            domain_controller = 'EGCDC05'
        else:
            domain_controller = 'EGCDC07'

        # Connect to the Domain Controller
        session = winrm.Session(domain_controller, auth=(username, password), transport="ntlm")
        logging.info(f"Successfully connected to the Domain Controller: {domain_controller}")

        # Determine the target OU path based on the domain
        if domain == "ESI":
            target_path = "OU=Servers,OU=WCL,DC=esi,DC=us,DC=eisai,DC=local"
        elif domain == "EGC":
            target_path = "OU=Servers,OU=WCL,DC=egc,DC=us,DC=eisai,DC=local"
        else:
            logging.error("Invalid domain specified.")
            return False

        # Construct the PowerShell command
        command = f"""
        $Computer = Get-ADComputer -Identity '{computer_name}' | Select-Object -First 1
        Move-ADObject -Identity $Computer -TargetPath '{target_path}'
        """

        logging.info(f"Executing command on Domain: {domain.upper()} - Domain Controller: {domain_controller}")

        # Run the PowerShell script
        file_command = session.run_ps(command)

        # Get the status code
        status_code = file_command.status_code
        logging.info(f"Command status code: {status_code}")

        if status_code == 0:
            logging.info(f"Computer object {computer_name} successfully moved to WCL/Server OU")
            return True
        else:
            logging.error(f"Failed to move computer object. Status code: {status_code}")
            if file_command.std_err:
                logging.error(f"Error details: {file_command.std_err.decode('utf-8')}")
            return False

    except Exception as e:
        import traceback
        logging.error(f"Error moving computer object: {e}")
        logging.error(traceback.format_exc())
        return False



if __name__ == "__main__":
    # Check arguments
    if len(sys.argv) < 7:
        logging.error("Usage: python step6_move_computer_ou.py HOST USERNAME PASSWORD NEW_HOSTNAME DOMAIN LOCATION")
        logging.error("Example: python step6_move_computer_ou.py ************* domain\\admin Password123 NEWSERVER01 ESI COLO")
        sys.exit(1)

    # Get arguments
    host = sys.argv[1]
    username = sys.argv[2]
    password = sys.argv[3]
    new_hostname = sys.argv[4]
    domain = sys.argv[5]
    location = sys.argv[6]

    if domain == "ESI":
        domain_name = "ESI.US.EISAI.LOCAL"
    elif domain == "EGC":
        domain_name = "EGC.US.EISAI.LOCAL"

    # Run the move computer OU operation directly
    success = move_computer_object(username, password, domain_name, location, new_hostname)

    # Exit with appropriate code
    sys.exit(0 if success else 1)
