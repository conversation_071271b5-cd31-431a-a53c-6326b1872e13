#!/usr/bin/env python
"""
Global Arguments Module

This module provides standardized command-line argument handling for all step scripts.
All scripts will use the same argument structure for consistency.

Standard Usage:
  python stepX_scriptname.py HOST USERNAME PASSWORD [STEP_SPECIFIC_ARGS...]

Standard Arguments (positions 1-3 for all scripts):
  1. HOST - Target Windows server IP address or hostname
  2. USERNAME - WinRM connection username (domain\username format)  
  3. PASSWORD - WinRM connection password

Step-Specific Arguments (positions 4+):
  Step 1: NEW_HOSTNAME
  Step 2: IP_ADDRESS SUBNET_MASK GATEWAY DNS_SERVERS
  Step 3: (no additional args)
  Step 4: (no additional args)
  Step 5: DOMAIN DOMAIN_USERNAME DOMAIN_PASSWORD
  Step 6: NEW_HOSTNAME DOMAIN
  Step 7: DOMAIN_SHORT LOCATION
  Step 8: (no additional args)
  Step 9: (no additional args)
"""
import sys
import logging

class GlobalArgs:
    """Global arguments class for standardized argument handling"""
    
    def __init__(self, script_name, min_args=4):
        self.script_name = script_name
        self.min_args = min_args
        self.args = sys.argv[1:] if len(sys.argv) > 1 else []
        
    def validate_args(self):
        """Validate minimum number of arguments"""
        if len(sys.argv) < self.min_args:
            self.print_usage()
            sys.exit(1)
    
    def get_common_args(self):
        """Get the first 3 common arguments (HOST, USERNAME, PASSWORD)"""
        if len(self.args) < 3:
            self.print_usage()
            sys.exit(1)
        
        return {
            'host': self.args[0],
            'username': self.args[1], 
            'password': self.args[2]
        }
    
    def get_step_args(self, start_index=3):
        """Get step-specific arguments starting from index"""
        return self.args[start_index:] if len(self.args) > start_index else []
    
    def print_usage(self):
        """Print usage information based on script name"""
        usage_map = {
            'step1_rename_server.py': {
                'usage': 'python step1_rename_server.py HOST USERNAME PASSWORD NEW_HOSTNAME',
                'example': 'python step1_rename_server.py ************* domain\\admin Password123 NEWSERVER01',
                'args': ['HOST', 'USERNAME', 'PASSWORD', 'NEW_HOSTNAME']
            },
            'step2_update_ip_config.py': {
                'usage': 'python step2_update_ip_config.py HOST USERNAME PASSWORD IP_ADDRESS SUBNET_MASK GATEWAY DNS_SERVERS',
                'example': 'python step2_update_ip_config.py ************* domain\\admin Password123 *********** 24 ********** "************,************"',
                'args': ['HOST', 'USERNAME', 'PASSWORD', 'IP_ADDRESS', 'SUBNET_MASK', 'GATEWAY', 'DNS_SERVERS']
            },
            'step3_disable_ipv6.py': {
                'usage': 'python step3_disable_ipv6.py HOST USERNAME PASSWORD',
                'example': 'python step3_disable_ipv6.py ************* domain\\admin Password123',
                'args': ['HOST', 'USERNAME', 'PASSWORD']
            },
            'step4_disable_print_spooler.py': {
                'usage': 'python step4_disable_print_spooler.py HOST USERNAME PASSWORD',
                'example': 'python step4_disable_print_spooler.py ************* domain\\admin Password123',
                'args': ['HOST', 'USERNAME', 'PASSWORD']
            },
            'step5_join_domain.py': {
                'usage': 'python step5_join_domain.py HOST USERNAME PASSWORD DOMAIN DOMAIN_USERNAME DOMAIN_PASSWORD',
                'example': 'python step5_join_domain.py ************* domain\\admin Password123 ESI.US.EISAI.LOCAL domain\\admin DomainPass123',
                'args': ['HOST', 'USERNAME', 'PASSWORD', 'DOMAIN', 'DOMAIN_USERNAME', 'DOMAIN_PASSWORD']
            },
            'step6_move_computer_ou.py': {
                'usage': 'python step6_move_computer_ou.py HOST USERNAME PASSWORD NEW_HOSTNAME DOMAIN',
                'example': 'python step6_move_computer_ou.py ************* domain\\admin Password123 NEWSERVER01 ESI.US.EISAI.LOCAL',
                'args': ['HOST', 'USERNAME', 'PASSWORD', 'NEW_HOSTNAME', 'DOMAIN']
            },
            'step7_update_dns.py': {
                'usage': 'python step7_update_dns.py HOST USERNAME PASSWORD DOMAIN_SHORT LOCATION',
                'example': 'python step7_update_dns.py ************* domain\\admin Password123 ESI COLO',
                'args': ['HOST', 'USERNAME', 'PASSWORD', 'DOMAIN_SHORT', 'LOCATION']
            },
            'step8_activate_windows.py': {
                'usage': 'python step8_activate_windows.py HOST USERNAME PASSWORD',
                'example': 'python step8_activate_windows.py ************* domain\\admin Password123',
                'args': ['HOST', 'USERNAME', 'PASSWORD']
            },
            'step9_check_windows_updates.py': {
                'usage': 'python step9_check_windows_updates.py HOST USERNAME PASSWORD',
                'example': 'python step9_check_windows_updates.py ************* domain\\admin Password123',
                'args': ['HOST', 'USERNAME', 'PASSWORD']
            }
        }
        
        if self.script_name in usage_map:
            info = usage_map[self.script_name]
            logging.error(f"Usage: {info['usage']}")
            logging.error(f"Example: {info['example']}")
            logging.error("Arguments:")
            for i, arg in enumerate(info['args'], 1):
                logging.error(f"  {i}. {arg}")
        else:
            logging.error(f"Usage: python {self.script_name} HOST USERNAME PASSWORD [ADDITIONAL_ARGS...]")
            logging.error(f"Example: python {self.script_name} ************* domain\\admin Password123")

def get_standardized_args(script_name, expected_arg_count):
    """
    Get standardized arguments for any step script
    
    Args:
        script_name: Name of the calling script
        expected_arg_count: Total number of expected arguments (including HOST, USERNAME, PASSWORD)
    
    Returns:
        dict: Dictionary containing parsed arguments
    """
    args_handler = GlobalArgs(script_name, expected_arg_count)
    args_handler.validate_args()
    
    # Get common arguments
    common_args = args_handler.get_common_args()
    
    # Get step-specific arguments
    step_args = args_handler.get_step_args()
    
    # Combine all arguments
    all_args = {
        'host': common_args['host'],
        'username': common_args['username'],
        'password': common_args['password'],
        'step_args': step_args
    }
    
    return all_args

# Convenience functions for each step
def get_step1_args():
    """Get arguments for step1_rename_server.py"""
    args = get_standardized_args('step1_rename_server.py', 5)
    return {
        'host': args['host'],
        'username': args['username'],
        'password': args['password'],
        'new_hostname': args['step_args'][0] if args['step_args'] else None
    }

def get_step2_args():
    """Get arguments for step2_update_ip_config.py"""
    args = get_standardized_args('step2_update_ip_config.py', 8)
    return {
        'host': args['host'],
        'username': args['username'],
        'password': args['password'],
        'ip_address': args['step_args'][0] if len(args['step_args']) > 0 else None,
        'subnet_mask': args['step_args'][1] if len(args['step_args']) > 1 else None,
        'gateway': args['step_args'][2] if len(args['step_args']) > 2 else None,
        'dns_servers': args['step_args'][3] if len(args['step_args']) > 3 else None
    }

def get_step3_args():
    """Get arguments for step3_disable_ipv6.py"""
    args = get_standardized_args('step3_disable_ipv6.py', 4)
    return {
        'host': args['host'],
        'username': args['username'],
        'password': args['password']
    }

def get_step4_args():
    """Get arguments for step4_disable_print_spooler.py"""
    args = get_standardized_args('step4_disable_print_spooler.py', 4)
    return {
        'host': args['host'],
        'username': args['username'],
        'password': args['password']
    }

def get_step5_args():
    """Get arguments for step5_join_domain.py"""
    args = get_standardized_args('step5_join_domain.py', 7)
    return {
        'host': args['host'],
        'username': args['username'],
        'password': args['password'],
        'domain': args['step_args'][0] if len(args['step_args']) > 0 else None,
        'domain_username': args['step_args'][1] if len(args['step_args']) > 1 else None,
        'domain_password': args['step_args'][2] if len(args['step_args']) > 2 else None
    }

def get_step6_args():
    """Get arguments for step6_move_computer_ou.py"""
    args = get_standardized_args('step6_move_computer_ou.py', 6)
    return {
        'host': args['host'],
        'username': args['username'],
        'password': args['password'],
        'new_hostname': args['step_args'][0] if len(args['step_args']) > 0 else None,
        'domain': args['step_args'][1] if len(args['step_args']) > 1 else None
    }

def get_step7_args():
    """Get arguments for step7_update_dns.py"""
    args = get_standardized_args('step7_update_dns.py', 6)
    return {
        'host': args['host'],
        'username': args['username'],
        'password': args['password'],
        'domain_short': args['step_args'][0] if len(args['step_args']) > 0 else None,
        'location': args['step_args'][1] if len(args['step_args']) > 1 else None
    }

def get_step8_args():
    """Get arguments for step8_activate_windows.py"""
    args = get_standardized_args('step8_activate_windows.py', 4)
    return {
        'host': args['host'],
        'username': args['username'],
        'password': args['password']
    }

def get_step9_args():
    """Get arguments for step9_check_windows_updates.py"""
    args = get_standardized_args('step9_check_windows_updates.py', 4)
    return {
        'host': args['host'],
        'username': args['username'],
        'password': args['password']
    }
