# Step 6 Improvements: Automated Computer Object Movement

## Overview
Step 6 has been completely rewritten to automatically move computer objects to the WCL/Server OU instead of providing manual instructions.

## Changes Made

### **1. Replaced Manual Instructions with Automation**

#### **Before:**
- Provided manual steps for using ADUC console
- Required manual intervention on domain controller
- No actual automation

#### **After:**
- Fully automated PowerShell-based computer object movement
- Direct connection to appropriate domain controller
- Automatic OU path determination based on domain

### **2. New Function: `move_computer_object()`**

```python
def move_computer_object(username, password, domain, location, computer_name):
    """Move Computer object to WCL/Server OU using PowerShell commands"""
```

**Features:**
- **5-minute delay** before execution (as per your logic)
- **Dynamic domain controller selection** based on domain and location
- **Automatic OU path determination** for ESI and EGC domains
- **PowerShell command execution** via WinRM
- **Comprehensive error handling** and logging

### **3. Domain Controller Selection Logic**

The script now automatically selects the correct domain controller based on domain and location:

#### **ESI Domain Controllers:**
- **ESI + COLO** → `esidcprod03`
- **ESI + Raleigh** → `RALDC01`
- **ESI + Baltimore** → `ESIBALDCWP01`
- **ESI + Nutley** → `ESINUTDCWP01`
- **ESI + AWS** → `ESIAWSNVDC02`

#### **EGC Domain Controllers:**
- **EGC + COLO** → `EGCDC03`
- **EGC + Nutley** → `EGCDC05`
- **EGC + Other** → `EGCDC07` (default)

### **4. Automatic OU Path Determination**

#### **ESI Domain:**
```
OU=Servers,OU=WCL,DC=esi,DC=us,DC=eisai,DC=local
```

#### **EGC Domain:**
```
OU=Servers,OU=WCL,DC=egc,DC=us,DC=eisai,DC=local
```

### **5. PowerShell Command Execution**

The script executes this PowerShell command on the domain controller:

```powershell
$Computer = Get-ADComputer -Identity 'COMPUTER_NAME' | Select-Object -First 1
Move-ADObject -Identity $Computer -TargetPath 'TARGET_OU_PATH'
```

### **6. Updated Usage**

#### **New Command Format:**
```bash
python step6_move_computer_ou.py HOST USERNAME PASSWORD NEW_HOSTNAME DOMAIN LOCATION

# Example:
python step6_move_computer_ou.py ************* domain\admin Password123 NEWSERVER01 ESI COLO
```

#### **Arguments:**
1. **HOST** - Target server IP/hostname (kept for consistency, not used)
2. **USERNAME** - Domain admin username
3. **PASSWORD** - Domain admin password
4. **NEW_HOSTNAME** - Computer name to move
5. **DOMAIN** - Domain (ESI or EGC)
6. **LOCATION** - Location (COLO, Raleigh, Baltimore, Nutley, AWS)

### **7. Enhanced Error Handling**

- **Connection validation** to domain controller
- **PowerShell command status checking**
- **Detailed error logging** with stack traces
- **Status code interpretation**

### **8. Logging Improvements**

All operations are now properly logged:

```python
logging.info("Waiting for 5 minutes before attempting connection...")
logging.info(f"Successfully connected to Domain Controller: {domain_controller}")
logging.info(f"Executing command on Domain: {domain.upper()}")
logging.info(f"Computer object {computer_name} successfully moved to WCL/Server OU")
```

## Benefits

### **1. Full Automation**
- ✅ No manual intervention required
- ✅ Automatic domain controller selection
- ✅ Direct PowerShell execution

### **2. Location Awareness**
- ✅ Supports multiple locations per domain
- ✅ Automatic domain controller mapping
- ✅ Flexible location handling

### **3. Robust Error Handling**
- ✅ Comprehensive exception handling
- ✅ Detailed error logging
- ✅ Status code validation

### **4. Consistent Interface**
- ✅ Follows same argument pattern as other steps
- ✅ Proper logging instead of print statements
- ✅ Standard exit codes

## Usage Examples

### **ESI Domain - COLO Location:**
```bash
python step6_move_computer_ou.py ************* domain\admin Password123 NEWSERVER01 ESI COLO
```

### **EGC Domain - Nutley Location:**
```bash
python step6_move_computer_ou.py ************* domain\admin Password123 NEWSERVER01 EGC Nutley
```

### **ESI Domain - AWS Location:**
```bash
python step6_move_computer_ou.py ************* domain\admin Password123 NEWSERVER01 ESI AWS
```

## Technical Details

### **Execution Flow:**
1. **Argument validation** - Check for required parameters
2. **5-minute delay** - Wait before connecting to domain controller
3. **Domain controller selection** - Choose appropriate DC based on domain/location
4. **WinRM connection** - Connect to selected domain controller
5. **OU path determination** - Set target OU based on domain
6. **PowerShell execution** - Run Move-ADObject command
7. **Status validation** - Check command execution results
8. **Logging and exit** - Log results and exit with appropriate code

### **Dependencies:**
- **winrm** module for PowerShell execution
- **Domain admin credentials** for AD operations
- **Network connectivity** to domain controllers
- **Active Directory PowerShell module** on domain controllers

## Integration with Rundeck

This step now integrates seamlessly with Rundeck:

### **Rundeck Job Options:**
```
DOMAIN_SHORT = ESI/EGC (Domain selection)
LOCATION = COLO/Raleigh/Baltimore/Nutley/AWS (Location selection)
COMPUTER_NAME = NEWSERVER01 (Computer name)
DOMAIN_USERNAME = domain\admin (Domain admin username)
DOMAIN_PASSWORD = [secure] (Domain admin password)
```

### **Rundeck Step Configuration:**
```bash
python step6_move_computer_ou.py ${option.TARGET_HOST} ${option.DOMAIN_USERNAME} ${option.DOMAIN_PASSWORD} ${option.COMPUTER_NAME} ${option.DOMAIN_SHORT} ${option.LOCATION}
```

The step is now fully automated and production-ready!
